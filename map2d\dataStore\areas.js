import { defineStore } from "pinia";
import { getMap2D } from "@map2d/singleton/index";

export const useMap2dAreaStore = defineStore("map2dAreaStore", {
  state: () => ({
    areas: new Map(), // 区域 除以下三个外的区域

    stopAreas: new Map(), // 急停区域
    speedLimitAreas: new Map(), // 限速区域
    clearRobotAreas: new Map(), // 区域

    selectStopAreaIds: new Set(), // 选中的急停区域id 快速查找|右侧面板
    selectSpeedLimitAreaIds: new Set(), // 选中的限速区域id 快速查找|右侧面板
    selectClearRobotAreaIds: new Set(), // 选中的禁行区域id 快速查找|右侧面板

    _isInitRenderAreas: false, // 是否初始化渲染了 急停区域 限速区域 清除区域

    areaChangeTimestamp: Date.now(), // 区域变化时间戳
    stopAreaChangeTimestamp: Date.now(), // 急停区域变化时间戳
    speedLimitAreaChangeTimestamp: Date.now(), // 限速区域变化时间戳
    clearRobotAreaChangeTimestamp: Date.now(), // 清除区域变化时间戳

    selectAroundStationAreaIds: new Set(), // 工作站和停靠点 排队区和缓冲区
    areaMsgList: [], // 区域消息列表 pop消息
  }),

  actions: {
    // 设置急停 限速 清除区域的颜色  areaId- 拼接的唯一id
    setAreaColor(type, areaId, color) {
      let color16 = $utils.Tools.getColor16(color);
      let storeItem;
      switch (type) {
        case "STOP":
          storeItem = this.stopAreas.get(areaId);
          color16 = 0xff0000; //color16 ||
          break;
        case "SPEED_LIMIT":
          storeItem = this.speedLimitAreas.get(areaId);
          color16 = 0xfbce2c; //color16 ||
          break;
        case "CLEAR_ROBOT":
          storeItem = this.clearRobotAreas.get(areaId);
          color16 = 0xfbce2c; //color16 ||
          break;
      }

      if (storeItem) storeItem.color = color16;
    },

    // 设置选中的急停区域id
    addSelectStopAreaId(id) {
      this.selectStopAreaIds.add(id);
      const item = this.stopAreas.get(id);
      this._renderStopArea(id, item, true);
    },
    // 删除选中的【急停】区域id
    deleteSelectStopAreaId(id) {
      this.selectStopAreaIds.delete(id);

      const item = this.stopAreas.get(id);
      if (item?.systemState != "RUNNING") return; // 区域状态【急停中】的话，不需要移除
      this._renderStopArea(id, item, false);
    },
    // 清空选中的【急停】区域id
    clearSelectStopAreaIds() {
      const list = this.selectStopAreaIds;
      if (list.size == 0) return;
      list.forEach(areaId => {
        const item = this.stopAreas.get(areaId);
        this._renderStopArea(areaId, item, item.systemState != "RUNNING");
      });
      list.clear();
    },

    // 设置选中的【限速】区域id
    addSelectSpeedLimitAreaId(id) {
      this.selectSpeedLimitAreaIds.add(id);
      const item = this.speedLimitAreas.get(id);
      this._renderSpeedLimitAreas(id, item, true);
    },
    // 删除选中的【限速】区域id
    deleteSelectSpeedLimitAreaId(id) {
      this.selectSpeedLimitAreaIds.delete(id);

      const item = this.speedLimitAreas.get(id);

      if (!!item.active) return; // 区域状态【限速中】的话，不需要移除
      this._renderSpeedLimitAreas(id, item, false);
    },
    // 清空选中的【限速】区域id
    clearSelectSpeedLimitAreaIds() {
      const list = this.selectSpeedLimitAreaIds;
      if (list.size == 0) return;
      list.forEach(areaId => {
        const item = this.speedLimitAreas.get(areaId);

        this._renderSpeedLimitAreas(areaId, item, !!item.active);
      });
      list.clear();
    },

    // 设置选中的【清除】区域id
    addSelectClearRobotAreaId(id) {
      this.selectClearRobotAreaIds.add(id);
      const item = this.clearRobotAreas.get(id);
      this._renderClearRobotAreas(id, item, true);
    },
    // 删除选中的【清除】区域id
    deleteSelectClearRobotAreaId(id) {
      this.selectClearRobotAreaIds.delete(id);

      const item = this.clearRobotAreas.get(id);
      if (item.systemState == "CLEARING") return; // 区域状态【清除中】的话，不需要移除
      this._renderClearRobotAreas(id, item, false);
    },
    // 清空选中的【清除】区域id
    clearSelectClearRobotAreaIds() {
      const list = this.selectClearRobotAreaIds;
      if (list.size == 0) return;
      list.forEach(areaId => {
        const item = this.clearRobotAreas.get(areaId);
        this._renderClearRobotAreas(areaId, item, item.systemState == "CLEARING");
      });
      list.clear();
    },

    // 设置选中的 工作站/停靠点的【排队区/缓冲区】
    addSelectAroundStationAreaId(id) {
      this.selectAroundStationAreaIds.add(id);
      const item = this.areas.get(id);
      this._renderAroundStationArea(id, item, true);
    },
    // 设置选中的 工作站/停靠点的【排队区/缓冲区】
    deleteSelectAroundStationAreaId(id) {
      this.selectAroundStationAreaIds.delete(id);
      const item = this.areas.get(id);
      this._renderAroundStationArea(id, item, false);
    },
    // 设置选中的 工作站/停靠点的【排队区/缓冲区】
    clearSelectAroundStationAreaIds() {
      const list = this.selectAroundStationAreaIds;
      if (list.size == 0) return;
      list.forEach(areaId => {
        const item = this.areas.get(areaId);
        this._renderAroundStationArea(areaId, item, false);
      });
      list.clear();
    },

    updateMapAreas(data) {
      //区域里有的快速查找里一定有
      if (!this._isInitRenderAreas) {
        this._initRenderMapAreas();
        this._isInitRenderAreas = true;
      }
      if (!data) return;
      const mapRender = getMap2D()?.mapRender;
      if (!mapRender) return;

      let stateAreas = this.areas;
      let stateStopAreas = this.stopAreas;
      let stateSpeedLimitAreas = this.speedLimitAreas;
      let stateClearRobotAreas = this.clearRobotAreas;

      const { mapAreas, speedLimitAreas, clearRobotAreas } = data;

      // mapAreas 585以后是Array（兼容 585的旧版object），里面有多个区域
      let item, areaId;
      for (let i = 0, len = mapAreas.length; i < len; i++) {
        item = mapAreas[i];
        areaId = this.getUniqueAreaId(item);
        if (item.areaType === "STOP") {
          const storeItem = stateStopAreas.get(areaId);
          item.color = storeItem.color;

          // 画急停区域
          if (!storeItem || storeItem.systemState != item.systemState) {
            this.selectStopAreaIds.clear(); // 清空选中的急停区域
            this._renderStopArea(areaId, item, item.systemState != "RUNNING");
          }
          // 保存急停区域
          stateStopAreas.set(areaId, item);
        }
        stateAreas.set(areaId, item);
      }

      // speedLimitAreas 是个数组，里面有多个限速区域
      speedLimitAreas.forEach(item => {
        const areaId = this.getUniqueAreaId(item);
        const storeItem = stateSpeedLimitAreas.get(areaId);
        item.color = storeItem.color;
        // 画限速区域
        if (!storeItem || storeItem.active != item.active) {
          this.selectSpeedLimitAreaIds.clear(); // 清空选中的限速区域
          this._renderSpeedLimitAreas(areaId, item, !!item.active);
        }
        // 保存限速区域
        stateSpeedLimitAreas.set(areaId, item);
      });

      // // clearAreas 是个数组，里面有多个清除区域
      clearRobotAreas.forEach(item => {
        const areaId = this.getUniqueAreaId(item);
        const storeItem = stateClearRobotAreas.get(areaId);
        item.color = storeItem.color;
        // 画清除区域
        if (!storeItem || storeItem.systemState != item.systemState) {
          this.selectClearRobotAreaIds.clear(); // 清空选中的清除区域
          this._renderClearRobotAreas(areaId, item, item.systemState == "CLEARING");
        }
        // 保存清除区域
        stateClearRobotAreas.set(areaId, item);
      });

      this.areaChangeTimestamp = Date.now();
      this.stopAreaChangeTimestamp = Date.now();
      this.speedLimitAreaChangeTimestamp = Date.now();
      this.clearRobotAreaChangeTimestamp = Date.now();
    },

    // 初始化的时候不在这里存数据 存了会导致update的状态和初始化状态一样，不绘制区域
    setMapAreas(data) {
      if (!data) return;
      let stateAreas = new Map();
      let stateStopAreas = new Map();
      let stateSpeedLimitAreas = new Map();
      let stateClearRobotAreas = new Map();

      const { mapAreas, speedLimitAreas, clearRobotAreas } = data;

      // mapAreas 585以后是Array（兼容 585的旧版object），里面有多个区域
      let item, areaId;
      for (let i = 0, len = mapAreas.length; i < len; i++) {
        item = mapAreas[i];
        areaId = this.getUniqueAreaId(item);
        if (item.areaType === "STOP") {
          // 保存急停区域
          stateStopAreas.set(areaId, item);
        }
        stateAreas.set(areaId, item);
      }

      // speedLimitAreas 是个数组，里面有多个限速区域
      speedLimitAreas.forEach(item => {
        const areaId = this.getUniqueAreaId(item);
        // 保存限速区域
        stateSpeedLimitAreas.set(areaId, item);
      });

      // clearAreas 是个数组，里面有多个清除区域
      clearRobotAreas.forEach(item => {
        const areaId = this.getUniqueAreaId(item);
        // 保存清除区域
        stateClearRobotAreas.set(areaId, item);
      });

      this.areas = stateAreas;
      this.stopAreas = stateStopAreas;
      this.speedLimitAreas = stateSpeedLimitAreas;
      this.clearRobotAreas = stateClearRobotAreas;

      this.areaChangeTimestamp = Date.now();
      this.stopAreaChangeTimestamp = Date.now();
      this.speedLimitAreaChangeTimestamp = Date.now();
      this.clearRobotAreaChangeTimestamp = Date.now();
    },
    // 根据区域信息 获取区域唯一id
    getUniqueAreaId(item) {
      const areaType = item?.areaType || "";
      return `${item.floorId}_${item.areaId}_${areaType}`;
    },

    // 存储区域消息列表
    setAreaMsgList(data) {
      this.areaMsgList = data;
    },

    // 重置数据
    resetStore() {
      this.$reset();
    },

    // 初始化数据结束之后先init一波MapAreas
    _initRenderMapAreas() {
      const mapRender = getMap2D()?.mapRender;
      if (!mapRender) return;
      this.stopAreas.forEach((item, areaId) => {
        this._renderStopArea(areaId, item, item.systemState != "RUNNING");
      });

      this.speedLimitAreas.forEach((item, areaId) => {
        this._renderSpeedLimitAreas(areaId, item, !!item.active);
      });

      this.clearRobotAreas.forEach((item, areaId) => {
        this._renderClearRobotAreas(areaId, item, item.systemState == "CLEARING");
      });
    },

    _renderStopArea(areaId, item, isShow) {
      const { mapRender } = getMap2D();
      if (isShow) {
        mapRender?.renderBizArea(
          "stopAreas",
          {
            areaId,
            floorId: item.floorId,
            cellCodes: item.functionalArea,
            color: item.color,
          },
          true,
        );
      } else {
        mapRender?.renderBizArea("stopAreas", { areaId }, false);
      }
    },

    _renderSpeedLimitAreas(areaId, item, isShow) {
      const { mapRender } = getMap2D();

      if (isShow) {
        mapRender?.renderBizArea(
          "speedLimitAreas",
          {
            areaId,
            floorId: item.floorId,
            cellCodes: item.functionalArea,
            color: item.color,
          },
          true,
        );
      } else {
        mapRender?.renderBizArea("speedLimitAreas", { areaId }, false);
      }
    },

    _renderClearRobotAreas(areaId, item, isShow) {
      const { mapRender } = getMap2D();

      if (isShow) {
        mapRender?.renderBizArea(
          "clearRobotAreas",
          {
            areaId,
            floorId: item.floorId,
            cellCodes: item.functionalArea,
            color: item.color,
          },
          true,
        );
      } else {
        mapRender?.renderBizArea("clearRobotAreas", { areaId }, false);
      }
    },

    // 渲染  工作站和停靠点 【排队区和缓冲区】
    _renderAroundStationArea(areaId, item, isShow) {
      const { mapRender } = getMap2D();
      if (isShow) {
        mapRender?.renderBizArea(
          "aroundStationAreas",
          {
            areaId,
            floorId: item.floorId,
            cellCodes: item.functionalArea,
            color: item.color || 0xff0000,
          },
          true,
        );
      } else {
        mapRender?.renderBizArea("aroundStationAreas", { areaId }, false);
      }
    },
  },
});
