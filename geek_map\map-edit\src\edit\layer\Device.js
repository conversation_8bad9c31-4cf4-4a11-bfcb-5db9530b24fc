import Base from "./Base";
import StationElement from "../element/Station";
import ChargeElement from "../element/Charge";
import MarkerElement from "../element/Marker";
import ElevatorElement from "../element/Elevator";
import SafeElement from "../element/Safe";
import ConveyorElement from "../element/Conveyor";
import LiftElement from "../element/Lift";
import ForkElement from "../element/Fork";
import MoveForkElement from "../element/MoveFork";
import StackerElement from "../element/Stacker";

import Store from "../store/Store";
const elementRenderMap = new Map([
  ["STATION", StationElement],
  ["CHARGER", ChargeElement],
  ["MARKER", MarkerElement],
  ["ELEVATOR", ElevatorElement],
  ["SAFE", SafeElement],
  ["CONVEYOR", ConveyorElement],
  ["FORK", ForkElement],
  ["LIFT", LiftElement],
  ["MOVEFORK", MoveForkElement],
  ["STACKER", StackerElement],
]);
export default class Device extends Base {
  constructor() {
    super();
  }
  //初始化元素
  initElements(data = []) {
    const { layerName } = this;
    const elementRender = elementRenderMap.get(layerName);
    data.forEach(item => {
      const { id, cellCode } = item;
      //初始化的数据都是有cellCode的，cellCode即为设备与单元格的关联id
      const relatedId = cellCode;

      const $el = elementRender.add(item);
      if ($el) {
        $el.layerName = layerName;
        this.container.addChild($el);
        this.setProperties(id, $el, { ...item, deviceType: layerName });
        //添加设备与cellCode的关系
        if (cellCode) Store.cellCode2Device.insert(relatedId, { layerName, id });
      }
    });
  }
  //添加元素
  addElements(data = []) {
    const { layerName } = this;
    const elementRender = elementRenderMap.get(layerName);
    //历史数据
    const historyData = [];
    const addedData = [];
    data.forEach(item => {
      const { id, cellCode, mapEditItemId } = item;
      const relatedId = cellCode || mapEditItemId;
      const $el = elementRender.add(item);
      $el.layerName = this.layerName;
      this.container.addChild($el);
      this.setProperties(id, $el, item);
      historyData.push(item);
      addedData.push(this.getProperties(id));
      //添加设备与cellCode的关系,新添加的设备单元格，是没有cellCode的，但是有nodeId,所以只要有其一，都可以作为设备和单元格关联的值
      if (relatedId) {
        Store.cellCode2Device.insert(relatedId, { layerName, id });
      }
    });
    //添加历史记录
    const historyDetail = { action: "add", detail: historyData, layerName };
    return { historyDetail, emitData: addedData };
  }
  //更新元素
  updateElements(data = [], isCoverProperties = false) {
    const { layerName } = this;
    const elementRender = elementRenderMap.get(layerName);
    //历史数据
    const historyData = [];
    const updateData = [];
    data.forEach(item => {
      const { id } = item;
      //旧数据存储
      const { properties: oldProperties } = this.getProperties(id);
      //解除与旧的cellCode的联系
      const { cellCode: oldCellCode, mapEditItemId: oldMapEditItemId } = { ...oldProperties };
      const oldRelatedId = oldCellCode || oldMapEditItemId;
      historyData.push({ ...oldProperties });

      const $el = this.id2$el.get(id);
      this.setProperties(id, $el, item, isCoverProperties);
      const updateInfo = this.getProperties(id);
      const { properties: newProperties } = updateInfo;
      const { cellCode: newCellCode, mapEditItemId: newMapEditItemId } = newProperties;
      const newRelatedId = newCellCode || newMapEditItemId;
      elementRender.update($el, newProperties);
      //添加设备与cellCode的关系
      if (oldRelatedId !== newRelatedId) Store.cellCode2Device.delete(oldRelatedId, { layerName, id });
      if (newRelatedId) Store.cellCode2Device.insert(newRelatedId, { layerName, id });
      updateData.push({ ...updateInfo });
    });
    //添加历史记录
    const historyDetail = { action: "update", detail: historyData, layerName };
    return { historyDetail, emitData: updateData };
  }
  //删除元素
  deleteElements(ids = []) {
    const { layerName } = this;
    //历史数据
    const historyData = [];
    const deleteData = [];
    ids.forEach(id => {
      const { $el, properties } = this.getProperties(id);
      const { cellCode, mapEditItemId } = properties;
      const relatedId = cellCode || mapEditItemId;
      Store.cellCode2Device.delete(relatedId, { layerName, id });
      historyData.push({ ...properties });
      deleteData.push({ $el: null, properties: { ...properties } });
      this.id2$el.delete(id);
      this.container.removeChild($el);
    });
    const historyDetail = { action: "delete", detail: historyData, layerName };
    return { historyDetail, emitData: deleteData };
  }
  //是否可以被点击
  triggerLayer(isTrigger) {
    this.container.interactiveChildren = isTrigger;
  }
}
