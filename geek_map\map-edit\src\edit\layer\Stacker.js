import Base from "./Base";
import Device from "./Device";
import StackerElement from "../element/Stacker";
import EventBus from "../eventBus/EventBus";
class Stacker extends Device {
  constructor() {
    super();
    this.layerName = "STACKER";
  }
  // initElements(data = []){
  //   data.forEach(item => {
  //     const {id} = item
  //     const $el = StackerElement.add(item)
  //     if($el){
  //       this.container.addChild($el)
  //       this.setProperties(id,$el,item)
  //     }
  //   })
  // }
  // //添加元素
  // addElements(data = []){
  //   const addedData = []
  //   data.forEach(item => {
  //     const {id} = item
  //     const $el = StackerElement.add(item)
  //     if($el){
  //       this.container.addChild($el)
  //       this.setProperties(id,$el,item)
  //     }
  //     addedData.push(this.getProperties(id))
  //   })
  //   EventBus.$emit('added',addedData)
  // }
  // //更新元素
  // updateElements(data = []){
  //   const updateData = []
  //   data.forEach(item => {
  //     const {id} = item
  //     const $el = this.id2$el.get(id)
  //     this.setProperties(id,$el,item)
  //     const newProperties = this.getProperties(id)
  //     const {properties} = newProperties
  //     StackerElement.update($el,properties)
  //     updateData.push(newProperties)
  //   })
  //   EventBus.$emit('updated',updateData)
  // }

  //是否可以被点击
  triggerLayer(isTrigger) {
    // this.container.interactiveChildren = isTrigger;
  }
}
export default Stacker;
