<template>
  <!-- ⭐ 在每个关键节点加入ref属性, 可以被mapVNode.ts中的函数捕获 ⭐ -->
  <div class="editMap">
    <!-- 头部面板 -->
    <div class="headerPanels" v-loading="!i18nReady">
      <MapToolPanel
        ref="headerPanels"
        v-model="topToolOption"
        :config="{ defActive: true }"
        border
        @trigger="triggerEvent"
      />
      <div>
        <!-- 快速查找 -->
        <MapGlobalSearch ref="globalSearchRef" />
        <!-- 顶部菜单按钮 -->
        <MapTopRightPanel :model-value="topRightPanelList" />
      </div>
    </div>

    <div class="content">
      <!-- 左侧面板 -->
      <div class="leftPanels" v-loading="!i18nReady">
        <MapToolPanel
          ref="leftPanels"
          v-model="leftToolOption"
          :config="{ align: 'left', model: 'active' }"
          border
          @trigger="triggerEvent"
        />
      </div>

      <!-- 画布 -->
      <div class="editMapCanvas" v-loading="isGlobalLoading" element-loading-text="Loading...">
        <!-- 帧率 -->
        <!-- <frameRate /> -->
        <MapCanIcon v-model="iconsOption" />
        <div ref="editRef" id="edit" v-show="!isMapInitResultError"></div>

        <!-- 初始化需要的数据没有准备好 -->
        <div class="empty" v-if="!ready">
          <el-empty :description="$t('lang.rms.fed.notMapFloorIdMsg')"> </el-empty>
        </div>

        <!-- 初始化失败了 -->
        <el-result
          class="mapInitResultError"
          v-if="!!isMapInitResultError"
          :title="$t('lang.rms.fed.failInitMapMsg')"
          icon="error"
        >
          <template #sub-title>
            <div class="subTitle">
              {{ $t("lang.rms.fed.plCheckWhMapFloorExMsg") }}
            </div>
            <el-alert
              type="error"
              :closable="false"
              :description="$t(isMapInitResultError || '')"
            />
          </template>
          <template #extra>
            <el-button type="primary" @click="readyCallback">{{
              $t("lang.common.retry")
            }}</el-button>
          </template>
        </el-result>
      </div>

      <!-- 右侧面板 -->
      <div class="rightPanels" v-loading="!i18nReady">
        <MapAttrPanel ref="rightPanels" />
      </div>
    </div>

    <!-- 底部面板/还没有内容 先预设 -->
    <div class="floorPanels"></div>

    <!-- 鼠标右键产生的菜单 -->
    <MapMouseMenuVue ref="mapMouseMenu" />
    <!-- 保存校验弹框 -->
    <MapValidateDialog ref="mapValidateDialog" />
    <!-- 非正式地图提示 -->
    <el-tooltip
      placement="right"
      effect="dark"
      :content="$t('lang.rms.fed.map.unofficialVersionTooltip')"
    >
      <el-button v-if="![1, 2, 3].includes(isSeal)" type="danger" class="map-info">{{
        $t("lang.rms.result.map.version.informal")
      }}</el-button>
    </el-tooltip>
    <!-- 底部地图信息 -->
    <MapBottomInfo></MapBottomInfo>
  </div>
</template>

<script setup lang="ts">
/**
 * UI入口
 * 结构分为上/下/左/右 和地图绘制区 5块
 */

// 组件
import MapToolPanel from "@packages/components/map-panel-tool/index.vue";
import MapAttrPanel from "@packages/components/map-panel-attr/index.vue";
import MapTopRightPanel from "@packages/components/map-topRight-attr/index.vue";
import MapCanIcon from "@packages/components/map-canvas-icon/index.vue";
import { MapMouseMenuVue } from "@packages/components/map-mouse-menu/index";
import MapValidateDialog from "@packages/components/map-validate-dialog/index.vue";
import MapGlobalSearch from "@packages/components/map-global-search/index.vue";
import MapBottomInfo from "@packages/components/map-bottom-info/index.vue";
// 组件配置
import { toolPanelList, showPanelList } from "../../configure/toolPanel.conf";
import { topRightPanelList } from "@packages/configure/topRightPanels.conf";
import { iconConf } from "../../configure/canvasIcons.conf";

// 全局数据
import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";
import { useAttrStore } from "@packages/store/attr";
import { useAppStore } from "@packages/store/app";

// Map初始化与获取
import {
  initEditMap,
  loadEditMapData,
  initMapSetup,
  loadMapData,
  useEditMap,
} from "@packages/hook/useEdit";
import { triggerEvent, editBindEventBus } from "@packages/hook/useEvent";

// vue
import { ref, Ref, computed, ComputedRef, watch, onMounted } from "vue";
const { t } = useI18n();
import { useTemplateStore } from "@packages/store/template";
const attrStore = useAttrStore();
const appStore = storeToRefs(useAppStore());

// 接口数据异常的flag
const isMapInitResultError: Ref<string> = ref("");
const editRef = ref();
/* **** *
 * data *
 * **** */
const iconsOption = ref(iconConf);
const topToolOption = ref(toolPanelList);
const leftToolOption = ref(showPanelList);

watch(
  () => appStore.mapLeftToolConfig,
  value => {
    value && (leftToolOption.value = value as any);
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  () => appStore.mapTopToolConfig,
  value => {
    value && (topToolOption.value = value as any);
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  () => appStore.mapIconConfig,
  value => {
    value && (iconsOption.value = value as any);
  },
  {
    immediate: true,
    deep: true,
  },
);

// 是否处于ready状态
const ready: ComputedRef<boolean> = computed(() => {
  return !!(appStore.mapId?.value && appStore.floorId?.value);
});

// i18n是否加载完成
const i18nReady: ComputedRef<boolean> = computed(() => {
  return t("lang.common.success") !== "lang.common.success";
});

// loading状态
const isGlobalLoading: ComputedRef<boolean> = computed(() => attrStore.globalLoading);

/**
 * 等待页面加载完成后初始化editMap组件
 * 这里如果页面加载完成后mapId和floorId还没有准备好, 则需要等待数据加载完成后继续
 */
const isSeal = ref(1);
onMounted(async () => {
  // 初始化地图
  await initEditMap(editRef.value);
  // 加载非必要数据
  loadMapData();

  // 检查必须参数的准备情况
  if (ready.value) {
    readyCallback();
  } else {
    watch(ready, value => {
      value && readyCallback();
    });
  }
});

/**
 * 加载地图的最后一步, 初始化地图组件
 */
async function readyCallback() {
  attrStore.setGlobalLoading(true);
  try {
    const data = await initMapSetup();
    attrStore.setGlobalLoading(false);
    isMapInitResultError.value = "";
    loadEditMapData(data);
    //赋值是否封板
    const templateStore = useTemplateStore();
    isSeal.value = templateStore.status;
    // 注册事件
    editBindEventBus();

    // 向调用者传递 ready
    window.parent?.postMessage({
      system: "geekplus-fe",
      action: "pass_data",
      params: {
        type: "ready",
      },
    });

    // 有些情况下会出现地图消失的问题, 在本地难以复现, 这里做一个延迟处理, 去调用居中即可正常展示
    setTimeout(() => {
      useEditMap().value?.setCenter();
    }, 500);
  } catch (message) {
    attrStore.setGlobalLoading(false);
    if (typeof message === "string") {
      isMapInitResultError.value = message;
    } else if (message && typeof message === "object") {
      isMapInitResultError.value = ((message as any).message as string) || "";
      console.error(message);
    }

    // 加载失败, 向调用者传递失败
    window.parent?.postMessage({
      system: "geekplus-fe",
      action: "pass_data",
      params: {
        type: "loadFail",
        message,
      },
    });
  }
}
</script>

<script lang="ts">
import { setRef } from "@packages/hook/useMapVNode";
import { i18n } from "@packages/logics/i18n";
export default {
  created() {
    console.log("this >", this);
    setRef(this);
  },
};
</script>

<style scoped lang="scss">
.map-info {
  position: absolute;
  left: 40px;
  top: 70px;
  z-index: 100;
}
#edit {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
}
.empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.subTitle {
  font-size: 18px;
  padding-bottom: 10px;
}

.mapInitResultError {
  height: 100%;
  position: relative;
  z-index: 2;
}
</style>
<style lang="scss">
.editMap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  text-align: left;
  display: flex;
  position: relative;
  flex-direction: column;

  .headerPanels {
    min-height: 5px;
    position: relative;
  }

  .floorPanels {
    min-height: 5px;
    position: relative;
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: row;
    overflow: hidden;

    .leftPanels,
    .rightPanels {
      height: 100%;
      min-width: 5px;
      position: relative;
      .attrPanel.isExtend {
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .editMapCanvas {
      flex: 1;
      height: 100%;
      position: relative;
      overflow: hidden;
    }
  }

  .editMapToolTitleStyle {
    padding: 0 10px 0 5px;
    font-weight: bolder;
    color: #9ba4e4;
    font-size: 18px;
  }
}
</style>
