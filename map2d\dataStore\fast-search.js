import { defineStore } from "pinia";
import { getMap2D } from "@map2d/singleton/index";
import { useMap2dAreaStore } from "./areas";
import { reactive } from "vue";

export const useMap2dFastSearchStore = defineStore("map2dFastSearchStore", {
  state: () => ({
    needInit: true, // 是否初始化  因为后续 需要更细 rs动态分区数据 但是不需要处理其他区域
    fastSearchRequestTimer: null, // 快速查找tree的数据请求定时器
    treeOption: [], // 树形结构数据 -- 页面渲染用
    extraData: new Map(), // 存储一些 渲染数据 为了给treeOption减负
    pathTaskColorDic: [], // 路径任务集合 -- map2d 颜色数据
    pathDispatchColorDic: [], // 路径调度集合 -- map2d 颜色数据
    pathCustomColorDic: [], // 自定义颜色集合 -- map2d 颜色数据
    dynamicIds: [], // 需要动态 渲染的区域的id集合
  }),

  actions: {
    /* 请求 FastSearchRequestMsg
       1 生成  treeOption 数据  
       2 设置 急停 限速 清除区域的颜色
       注意 打开快速查找下来 每隔 60s 会重新请求一遍 fastSearchData 数据 
    */
    sendFastSearchRequestMsg(update = false, callback) {
      const mapWorker = getMap2D()?.mapWorker;
      if (!mapWorker) return;
      // 快速查找
      mapWorker.wsRequest("FastSearchRequestMsg", { floorIds: [], searchTypes: [] }).then(res => {
        if (res?.header?.code === 0) {
          if (!update) {
            this.initFastSearchData(res?.body);
          } else {
            this.updateFastSearchData(res?.body);
            callback && callback();
          }
        }
      });
    },

    /**
     *
     * @param {*} callback
     * @returns
     */
    sendDynAreaSearchRequestMsg(callback) {
      const mapWorker = getMap2D()?.mapWorker;
      if (!mapWorker) return;
      // 快速查找
      mapWorker
        .wsRequest("DynAreaSearchRequestMsg", { floorIds: [], searchTypes: [] })
        .then(res => {
          // console.log("请求动态数据", res);
          if (res?.header?.code === 0) {
            this.updateFastSearchData(res?.body);
            callback && callback();
          }
        });
    },

    /* - 获取 更新 快速查找数据 
       - 数据5分钟变更一次, 不用请求太频繁   
    */
    getUpdateFastSearchData(update, callback) {
      if (update) {
        this.clearFastSearchTimer();

        this.sendDynAreaSearchRequestMsg(callback);
        this.fastSearchRequestTimer = setTimeout(() => {
          this.getUpdateFastSearchData(true, callback);
        }, 60000); //60000
      } else {
        //点击展开下拉的时候 请求一次变化
        if (!this.fastSearchRequestTimer) {
          this.sendDynAreaSearchRequestMsg(callback);
        }
      }
    },

    // 清除快速查找定时器
    clearFastSearchTimer() {
      if (this.fastSearchRequestTimer) clearTimeout(this.fastSearchRequestTimer);
    },
    //初始化 快速查找数据
    initFastSearchData(data) {
      this.dynamicIds = []; // 清空动态分区的id集合
      this.needInit = true;
      this.treeOption = data.map(item => {
        // 一级分类 (1.功能点 2.路径类型 3.区域 4.任务类型 5调度类型)
        const { data, i18, type: firstLevelType } = item;
        const firstLevel = {
          label: i18,
          id: this._generateNodeId(item, firstLevelType),
          firstLevelType,
          disabled: true,
          class: "custom-tree-root-class", // 添加类名 用于区分一级分类
          children: [],
          type: firstLevelType,
        };

        switch (firstLevelType) {
          case "WAREHOUSE_AREA": // 区域
            this.extraData.set("WAREHOUSE_AREA", new Map()); // 存储区域数据 -- 用于地图渲染 或者 字段拼接的 data
            // 有 二级分类 （区域的类型）三级才是叶子节点具体的 区域id 相关数据 extraProperties
            firstLevel.children = data.map(level2 => {
              return {
                firstLevelType,
                id: this._generateNodeId(level2, firstLevelType),
                label: level2.i18,
                dataLength: level2.data ? level2.data.length : 0,
                isShowDataLen: true, // 是否显示数据长度
                type: level2.type,
                children: this._getAreaChildren(level2.data, level2.type, firstLevelType),
              };
            });

            break;
          default:
            const functionalCellExtraData = new Map();
            // 这几种 都只有一层 children
            firstLevel.children = data.map(level2 => {
              let color = level2.color ? $utils.Tools.getColor16(level2.color) : null;
              // 保存颜色
              if (firstLevelType === "JOB_TYPE") {
                this.pathTaskColorDic[parseInt(level2.type)] = color;
              } else if (firstLevelType === "DISPATCH_TYPE") {
                this.pathDispatchColorDic[parseInt(level2.type)] = color;
              } else if (firstLevelType === "CustomColor") {
                this.pathCustomColorDic[parseInt(level2.type)] = color;
              }

              if (firstLevelType === "FUNCTIONAL_CELL") {
                functionalCellExtraData.set(
                  this._generateNodeId(level2, firstLevelType),
                  level2.data,
                );
              }

              return {
                firstLevelType,
                id: this._generateNodeId(level2, firstLevelType),
                label: level2.i18,
                color: level2.color || "#999",
                type: level2.type,
              };
            });

            // 存储 用于地图渲染 或者 字段拼接的 data  -- 减负treeOption
            if (firstLevelType === "FUNCTIONAL_CELL") {
              this.extraData.set("FUNCTIONAL_CELL", functionalCellExtraData);
            }

            break;
        }

        //保存map2d 的 dic 数据
        const mapRender = getMap2D()?.mapRender;
        if (!mapRender) return;

        if (this.pathTaskColorDic) {
          mapRender.renderBiz("pathTaskColorDic", this.pathTaskColorDic, false);
        }
        if (this.pathDispatchColorDic) {
          mapRender.renderBiz("pathDispatchColorDic", this.pathDispatchColorDic, false);
        }
        if (this.pathCustomColorDic) {
          mapRender.renderBiz("pathCustomColorDic", this.pathCustomColorDic, false);
        }

        return firstLevel;
      });

      this.needInit = false;
    },

    // 更新快速查找数据 -- 目前只更新区域数据 --> RS动态分区数据
    updateFastSearchData(data) {
      this.dynamicIds = []; // 清空动态分区的id集合
      const flattenedAreas = data.filter(firstLevel => firstLevel.type === "WAREHOUSE_AREA")[0]
        ?.data;
      const updateArea = flattenedAreas.map(level2 => {
        return reactive({
          firstLevelType: "WAREHOUSE_AREA",
          id: this._generateNodeId(level2, "WAREHOUSE_AREA"),
          label: level2.i18,
          dataLength: level2.data ? level2.data.length : 0,
          isShowDataLen: true, // 是否显示数据长度
          type: level2.type,
          children: this._getAreaChildren(level2.data, level2.type, "WAREHOUSE_AREA"),
        });
      });
      // 直接替换整个 treeOption，确保响应式更新
      this.treeOption = this.treeOption.map(item =>
        item.firstLevelType === "WAREHOUSE_AREA"
          ? reactive({
              ...item,

              children: this.updateWarehouseArea(item.children, updateArea),
            })
          : reactive(item),
      );
    },

    // 更新仓库区域数据
    updateWarehouseArea(warehouseAreaData, updateArea) {
      return reactive(
        warehouseAreaData.map(item => {
          let newItem = null;
          switch (item.type) {
            case "ROBOT_RS":
              newItem = updateArea.find(area => area.type === "ROBOT_RS");
              return reactive({
                ...item,
                label: newItem?.label || item.label,
                children: newItem?.children,
              });
            case "BOX_LANE_PARTITION_AREA":
              newItem = updateArea.find(area => area.type === "BOX_LANE_PARTITION_AREA");
              return reactive({
                ...item,
                label: newItem?.label || item.label,
                children: newItem?.children,
              });
            default:
              return reactive(item);
          }
        }),
      );
    },

    /**
     * 由于快速查找的node变为了动态渲染, 会实时请求这里的数据
     * 如果用uuid, 每次变动都会导致所有的节点id变化, 没法回显, 而且重复渲染, 这里使用node的特征来生成id以避免此问题
     * firstLevelType 这里需要额外传入
     */
    _generateNodeId(item, firstLevelType) {
      const { i18, type } = item;
      return `${i18}_${firstLevelType}_${type}`;
    },

    // 获取 区域的子节点数据
    _getAreaChildren(areaData, areaType, rootType) {
      const map2dAreaStore = useMap2dAreaStore();
      const warehouseAreaExtraData = this.extraData.get("WAREHOUSE_AREA");
      let children = [];
      areaData.forEach(level3 => {
        const extraProperties = level3?.extraProperties;
        const areaId = extraProperties?.areaId;
        const name = level3?.name;
        if (!areaId) {
          const { warn } = console;
          warn("这个快速查找区域数据没有给areaId，不渲染，不处理", level3);
          return;
        }

        const uniqueId = map2dAreaStore.getUniqueAreaId(extraProperties); // 这里不能用uuid, 否则无法回填选中状态
        const color = "#999999"; //level3?.extraProperties?.color ||#ff0000
        const key = name ? `${areaType}_${areaId}(${name})` : `${areaType}_${areaId}`;
        warehouseAreaExtraData.set(uniqueId, level3);
        let childrenItem = {
          title: areaType,
          extraText: areaId,
          label: key,
          isLeaf: true,
          id: uniqueId,
          firstLevelType: rootType,
          color,
        };
        children.push(childrenItem);
        if (["BOX_LANE_PARTITION_AREA", "ROBOT_RS"].includes(areaType)) {
          // 这里是动态分区数据
          this.dynamicIds.push(uniqueId);
        }

        // 初始化区域颜色
        if (this.needInit && ["STOP", "SPEED_LIMIT", "CLEAR_ROBOT"].includes(areaType)) {
          map2dAreaStore.setAreaColor(areaType, uniqueId, color);
        }
      });

      return children;
    },
  },
});
