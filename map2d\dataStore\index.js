import { defineStore } from "pinia";
import { getMap2D } from "@map2d/singleton/index";

export const useMap2dStore = defineStore("map2dStore", {
  state: () => ({
    isWsBinaryClient: true, // 是否使用二进制传输
    isWsLoading: true, // 地图数据是否加载传输中
    isMapReady: false, // 地图是否初始化完成
    isRightCollapse: $utils.Data.getMap2dToolStatus("rightCollapse") || false, // 右侧面板是否折叠, 默认展开
    isRightOccupy: false, // 是否占用右侧面板, 默认不占用
    isRightTabLockMode: true, // 右侧面板是否全局, 默认全局锁定
    isRightDrawerVisible: false, // 是否显示右侧二级面板

    mapConfig: null, // 地图后端返回配置和数据信息
    mapElementsPos: null, // 元素位置信息
    mapClickData: null, // 地图点击数据 格式：{layer:'',code:'',floorId:''}
    mapClickStatus: { layer: [], isMulti: false }, // 地图点击状态

    wsQueryTimer: null, // websocket查询queryData的定时器
    wsDeadLockRobots: [], // 死锁机器人

    showExceptionDialog: null, // 是否显示异常弹窗 存{type:'',code:''} 用于请求接口
    loadingViewExImg: false, // 查看 异常图片的 loading
    imgClickType: "", // 图盘编辑 标注故障 click 的元素类型
  }),
  actions: {
    setWsBinaryClient(flag) {
      if (flag === this.isWsBinaryClient) return;
      this.isWsBinaryClient = flag;
    },
    setWsLoading(flag) {
      if (flag === this.isWsLoading) return;
      this.isWsLoading = flag;
    },
    setMapReady(flag) {
      if (flag === this.isMapReady) return;
      this.isMapReady = flag;
      if (flag) {
        const map2d = getMap2D();
        map2d?.mapRender?.setClickStatus(this.mapClickStatus);
      }
    },
    setRightCollapseState(flag) {
      if (flag === this.isRightCollapse) return;
      $utils.Data.setMap2dToolStatus("rightCollapse", flag);
      this.isRightCollapse = flag;
    },
    setRightOccupyState(flag) {
      if (flag === this.isRightOccupy) return;
      this.isRightOccupy = flag;
    },
    setRightTabLockMode(flag) {
      if (flag === this.isRightTabModeLock) return;
      $utils.Data.setMap2dToolStatus("tabLock", flag);
      this.isRightTabLockMode = flag;
    },
    setRightDrawerVisible(flag) {
      if (flag === this.isRightDrawerVisible) return;
      this.isRightDrawerVisible = flag;
    },

    setMapConfig(configs) {
      if (configs) {
        this.mapConfig = Object.assign({}, configs);
      }
    },
    setElementsPosition(data) {
      this.mapElementsPos = data;
    },
    setMapClickData(data) {
      this.mapClickData = data;
    },
    setMapClickStatus(data) {
      this.mapClickStatus = data;
      const map2d = getMap2D();
      map2d?.mapRender?.setClickStatus(data);
    },

    /**
     * 设置websocket 的 queryData 定时器
     * @param {any} timer -- 定时器
     * @description 组件之间的timer不共享，所以需要在store中保存timer，方便清除
     */
    setWsQueryTimer(timer) {
      if (timer == null && this.wsQueryTimer) {
        // 清除queryData 定时器
        clearTimeout(this.wsQueryTimer);
      }
      this.wsQueryTimer = timer;
    },
    setDeadLockRobots(data) {
      data = data || [];
      this.wsDeadLockRobots = data.map(item => {
        return { label: item, value: item };
      });
    },

    // 异常处理
    setViewExceptionImg(data) {
      if (!data) {
        this.showExceptionDialog = null;
        return;
      }
      this.loadingViewExImg = true;
      const { type, code } = data;
      let params = "";
      let option = null;
      switch (type) {
        case "robot":
          params = "robotId=" + code;
          option = { type, robotId: code, latticeCode: null };
          break;
        case "lattice":
          params = "latticeCode=" + code;
          option = { type, robotId: null, latticeCode: code };
          break;
      }
      $req
        .get("/athena/tools/fault/getFaultMessage?" + params)
        .then(res => {
          if (res.code === 0) {
            this.showExceptionDialog = Object.assign(res.data, option);
            this.loadingViewExImg = false;
          }
        })
        .catch(err => {
          this.loadingViewExImg = false;
          this.showExceptionDialog = null;
        });
    },

    // 图盘编辑 标注故障 click 的元素类型
    setImgClickType(type) {
      this.imgClickType = type;
    },

    resetMap2dStore() {
      this.$reset();
    },
  },
});
