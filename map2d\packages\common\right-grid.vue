<template>
  <div class="map-right-grid" :class="{ 'grid-column': !inline }">
    <label class="name" :class="[{ 'no-label': !label, required: isRequired }]">
      <em>*</em>{{ $t(label) }}
      <el-icon v-if="tips" :title="$t(tips)" class="tip-icon"><gp-icon-question /></el-icon>
    </label>
    <div class="value">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "Map2dRightGrid",
  props: {
    label: {
      // 名称
      type: String,
      default: "",
    },
    isRequired: {
      // 是否必填
      type: Boolean,
      default: false,
    },
    inline: {
      // 是否行内显示
      type: Boolean,
      default: true,
    },
    tips: {
      // 提示信息
      type: String,
      default: undefined,
    },
  },
};
</script>

<style lang="less" scoped>
.map-right-grid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  border-bottom: 1px #c8cdd3 solid;
  overflow: hidden;

  > label.name {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex: 0 0 80px;
    font-size: 12px;
    min-height: 28px;
    padding: 6px 3px;
    color: #8d97a5;
    text-align: right;
    overflow: hidden;
    word-break: break-word;
    line-height: 1.1;
    .tip-icon {
      font-size: 14px;
      margin-left: 2px;
      color: #558d9b;
      cursor: pointer;
    }

    &.no-label {
      flex: 0;
      padding: 0;
    }
    > em {
      display: none;
    }
    &.required > em {
      display: inline-block;
      content: "*";
      margin-right: 3px;
      color: #f24141;
    }
  }
  > div.value {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex: 1;
    font-size: 12px;
    padding: 3px;
    color: #000;
    word-break: break-all; /* 允许单词在边界处换行，但不会在单词内部折行 */
    :deep(.rms-input__inner) {
      padding: 0 5px;
      font-size: 12px;
    }
    :deep(.rms-select > .rms-input .rms-input__inner) {
      height: 26px !important;
    }

    .danger {
      color: #f24141;
    }

    .success {
      color: #00b44b;
    }

    .warning {
      color: #ff7f08;
    }
  }

  &.danger > div.value {
    color: #f24141;
  }

  &.success > div.value {
    color: #00b44b;
  }

  &.warning > div.value {
    color: #ff7f08;
  }

  &.grid-column {
    flex-wrap: wrap;
    > label.name {
      justify-content: flex-start;
      padding-bottom: 0;
      flex: 0 0 100%;
      min-height: 22px;
    }
  }
}
</style>
