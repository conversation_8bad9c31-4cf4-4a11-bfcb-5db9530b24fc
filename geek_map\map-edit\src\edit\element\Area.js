import {Graphics,Rectangle,FillStyle,LineStyle,GraphicsGeometry} from "pixi.js";
import Line from './baseElement/Line'
import Polygon from "./baseElement/Polygon";
import CtrlPoint from './baseElement/CtrlPoint'
//选中态
import {areaStyle} from '../config'
const {INACTIVE_AREA,ACTIVE_AREA} = areaStyle
//导入全局控制器
import Control from "../control/Control";
import Event from "../event/Event";
import EventBus from "../eventBus/EventBus";
import LayerManager from '../layerManager/LayerManager'
import {cad2pixi, pixi2cad, toFixed} from "../utils/utils";
import Selected from "../selected/Selected";

export default class Area {
  //拖拽的初始位置
  static dragStartPos = []
  //点的运算逻辑
  static pointCtrl() {
    //初始化非激活态的数据
    const initNoActivePath = (paths) => {
      const noActivePath = paths.map((p, pIndex) => {
        let fp, lp, posIndex;
        if (pIndex < (paths.length - 1)) {
          fp = p, lp = paths[pIndex + 1];
        } else {
          fp = paths[pIndex], lp = paths[0]
        }
        posIndex = pIndex + 0.5
        const {x: x1, y: y1} = fp
        const {x: x2, y: y2} = lp
        const obj = {x: (x1 + x2) / 2, y: (y1 + y2) / 2,pIndex:posIndex}
        return obj
      })
      return noActivePath
    }
    //对点进行排序，支持全部点和激活点两种
    const sort = ($el,types = ['point','noActivePoint']) => {
      const isRect = $el.isRect
      const $points = $el.children.filter(child => types.includes(child.name))
      if(isRect) return $points;
      //根据pIndex进行排序
      $points.sort((a, b) => {
        return a.pIndex - b.pIndex;
      });
      return $points
    }
    //获取相邻的点
    const getNearPoint = ($el,$nowP,offset = 1) => {
      const {pIndex} = $nowP
      const $points = $el.children.filter(child => ['point','noActivePoint'].includes(child.name))
      const len = $points.length
      let npIndex = pIndex + offset
      if(npIndex > (len - 1)) npIndex = npIndex - len;
      const $np = ($points.filter($p => $p.pIndex === npIndex))[0]
      //上一个点
      let lpIndex = pIndex - offset
      if(lpIndex < 0) lpIndex = lpIndex + len
      const $lp = ($points.filter($p => $p.pIndex === lpIndex))[0]
      return [{$p:$lp,pIndex:lpIndex},{$p:$np,pIndex: npIndex}]
    }
    //获取中心点
    const getCenterPos = ($p1,$p2) => {
      const {x:x1,y:y1} = $p1
      const {x:x2,y:y2} = $p2
      // const pIndex = (index1 + index2) / 2
      return {x:(x1 + x2) / 2,y:(y1 + y2) / 2}
    }
    //绑定激活点事件
    const bindActivePointEvent = ($el,$point) => {
      function pointerUpHandleFn($el,$point) {
        return function(e) {
          dragPointUpdate($el,$point)
          Event.eventCtrl().unbind($point,'pointerup',pointerUpHandle);
          Event.eventCtrl().unbind($point,'pointerupoutside',pointerUpHandle);
          Event.eventCtrl().unbind($point,'pointermove',pointerMoveHandler);
          console.log($point)
        }
      }
      function pointerMoveHandlerFn(ctx,$el,$point) {
        return function(e) {
          if ($point.dragging) {
            const newPosition = e.data.getLocalPosition($el);
            //设置为更新态
            if ($point.x !== newPosition.x || $point.y !== newPosition.y) {
              $point.isUpdate = true
            }
            $point.x = newPosition.x;
            $point.y = newPosition.y;
            //获取前后的未激活点位置，并且重新计算移动位置
            const {pIndex} = $point

            if ($el.isRect) {
              ctx._updateEditRect($el, pIndex)
            } else {
              const nearPoints1 = ctx.pointCtrl().getNearPoint($el,$point,1)
              const nearPoints2 = ctx.pointCtrl().getNearPoint($el,$point,2)
              nearPoints1.forEach((item,index) => {
                const {$p} = item
                const center = ctx.pointCtrl().getCenterPos($point,nearPoints2[index].$p)
                Object.assign($p,center)
              })
              ctx._updateEditPolygon($el)
            }
          }
        }
      }
      const pointerUpHandle = pointerUpHandleFn($el,$point)
      const pointerMoveHandler = pointerMoveHandlerFn(this,$el,$point)
      $point
        .on('pointerdown', (e) => {
          Control.enableDrag(false)
          $point.alpha = 0.5;
          $point.dragging = true;
          Event.eventCtrl().bind($point,'pointerup',pointerUpHandle);
          Event.eventCtrl().bind($point,'pointerupoutside',pointerUpHandle);
          Event.eventCtrl().bind($point,'pointermove',pointerMoveHandler);
        })
    }
    //绑定未激活点事件
    const bindNoActivePointEvent = ($el,$point) => {
      function pointerClickHandleFn(ctx,$el,$point) {
        return function(e) {
          if($point.name === 'point'){
            Event.eventCtrl().unbind($point,'pointerdown',pointerClickHandle)
            return
          }
          $point.alpha = 1
          e.stopPropagation()
          bindActivePointEvent($el,$point)
          $point.name = 'point'
          const {pIndex} = $point
          const $points = $el.children.filter(child => ['point','noActivePoint'].includes(child.name))
          const len = $points.length
          //下一个点
          const npIndex = pIndex === (len - 1) ? 0 : pIndex + 1
          const $np = ($points.filter($p => $p.pIndex === npIndex))[0]
          const pIndex1 = pIndex + 0.5
          //上一个点
          const lpIndex = pIndex === 0 ? (len  -1) : pIndex - 1
          const $lp = ($points.filter($p => $p.pIndex === lpIndex))[0]
          const pIndex2 = pIndex - 0.5

          const addPointArr = [[$point,$np,pIndex1],[$point,$lp,pIndex2]]
          addPointArr.forEach(arr => {
            const $p1 = arr[0]
            const $p2 = arr[1]
            const pIndex = arr[2]
            const {x,y} = ctx.pointCtrl().getCenterPos($p1,$p2)
            const $p = CtrlPoint.render(x, y)
            bindNoActivePointEvent($el,$p)
            $p.name = 'noActivePoint'
            $p.alpha = 0.5
            $p.zIndex = 3
            $p.pIndex = pIndex
            $el.addChild($p)
          })
          //从新排序赋值pIndex
          ctx._sortPoint($el)
        }
      }
      const pointerClickHandle = pointerClickHandleFn(this,$el,$point)
      Event.eventCtrl().bind($point,'pointerdown',pointerClickHandle)
      // $point
      //   .on('click',pointerClickHandle)
    }
    //拖拽更新
    const dragPointUpdate = ($el,$point) => {
      Control.enableDrag(true)
      $point.alpha = 1;
      $point.dragging = false;
      if ($point.isUpdate) {
        this._updateFinished($el)
        $point.isUpdate = false
      }
    }
    //渲染激活点
    const renderActivePoints = ($el,paths) => {
      const isSelected = $el.selected
      paths.forEach((p, pIndex) => {
        const {x, y} = p
        const $point = CtrlPoint.render(x, y)
        $point.visible = isSelected
        $point.name = 'point'
        //记录点的顺序，用来渲染
        $point.pIndex = pIndex
        //
        bindActivePointEvent($el,$point)

        $point.zIndex = 3
        $el.addChild($point)
      })
    }
    //渲染未激活点
    const renderNoActivePoints = ($el,paths) => {
      const isSelected = $el.selected
      const noActivePaths = this.pointCtrl().initNoActivePath(paths)
      noActivePaths.forEach(p => {
        const {x, y, pIndex} = p
        const $p = CtrlPoint.render(x, y)
        bindNoActivePointEvent($el,$p)
        $p.name = 'noActivePoint'
        $p.alpha = 0.5
        $p.visible = isSelected
        $p.zIndex = 3
        $p.pIndex = pIndex
        $el.addChild($p)
      })
    }
    return {
      sort,
      getNearPoint,
      getCenterPos,
      initNoActivePath,
      bindActivePointEvent,
      bindNoActivePointEvent,
      dragPointUpdate,
      renderActivePoints,
      renderNoActivePoints
    }
  }
  //创建面内单元格标识
  static areaInnerMarkerCtrl() {
    const w = 8,h = 8;
    const fillStyle = new FillStyle()
    const lineStyle = new LineStyle();
    // '#ea4e88'
    fillStyle.color = 0xea4e88
    fillStyle.visible = true;
    fillStyle.alpha = 0.8;
    lineStyle.visible = false;
    //渲染形状
    const renderShape = (location) => {
      const {x,y} = location
      const rect = new Rectangle(x - w/2,y-h/2,w,h)
      return rect
    }
    const create = ($con,cellCodes = []) => {
      clear($con)
      const graphicsGeometry = new GraphicsGeometry();
      const cellInstance = LayerManager.get('CELL')
      const info = cellInstance.getCellInfoByCellCodes(cellCodes || [])
      info.forEach(item => {
        const {location} = item
        const pixiLocation = cad2pixi(location)
        graphicsGeometry.drawShape(renderShape(pixiLocation),fillStyle,lineStyle)
      })
      graphicsGeometry.BATCHABLE_SIZE = info.length
      const $g = new Graphics(graphicsGeometry);
      $g.name = 'marker'
      $con.addChild($g)
      // return $g
    }
    const clear = ($con) => {
      const $marker = $con.getChildByName('marker')
      if($marker){
        $con.removeChild($marker)
      }
    }
    return {
      create,
      clear,
    }
  }


  //创建
  static add(data) {
    const {controlPoints,cellCodes, id, isRect} = data
    if (!controlPoints || controlPoints.length < 3) return null
    //将cad坐标转化为pixi坐标
    const pixiControlPoints = controlPoints.map(p => {
      return cad2pixi(p)
    })
    const $con = this._createEditPolygon(pixiControlPoints,isRect)
    this.areaInnerMarkerCtrl().create($con,cellCodes)
    $con.type = 'area'
    $con.id = id
    $con.isRect = isRect
    return $con
  }

  //更新
  static update($el, item) {
    //获取所有点位重新渲染
    const {controlPoints, id,cellCodes} = item
    //将cad坐标转化为pixi坐标
    const pixiControlPoints = controlPoints.map(p => {
      return cad2pixi(p)
    })
    const $points = $el.children.filter(child => ['point','noActivePoint'].includes(child.name))
    $el.removeChild(...$points)
    this.pointCtrl().renderActivePoints($el,pixiControlPoints)
    //渲染未激活点
    if(!$el.isRect){
      this.pointCtrl().renderNoActivePoints($el,pixiControlPoints)
    }
    this._sortPoint($el)
    // const $points = this.pointCtrl().sort($el,['point'])
    const $line = $el.getChildByName('line')
    const $polygon = $el.getChildByName('polygon')
    $line.clear()
    $polygon.clear()
    //闭合数据
    const closePaths = [...pixiControlPoints, pixiControlPoints[0]]
    const color = Selected.getSelected(id) ? ACTIVE_AREA : INACTIVE_AREA
    Line.render($line, closePaths, {color})
    Polygon.render($polygon, closePaths, {color})
    this.areaInnerMarkerCtrl().create($el,cellCodes)
  }

  //面渲染
  static _renderPolygon($el, paths) {
    const $line = $el.getChildByName('line')
    const $polygon = $el.getChildByName('polygon')
    //闭合数据
    const closePaths = [...paths, paths[0]]
    $line.clear()
    $polygon.clear()
    Line.render($line, closePaths, {color: ACTIVE_AREA})
    Polygon.render($polygon, closePaths, {color: ACTIVE_AREA})
  }

  //创建编辑面
  static _createEditPolygon(paths,isRect = false) {
    // if(paths.length < 3) return;
    //闭合数据
    const closePaths = [...paths, paths[0]]
    const $con = new Graphics();
    $con.interactive = true
    $con.interactiveChildren = true
    $con.cursor = 'pointer'
    $con.isRect = isRect
    //渲染线
    const $line = new Graphics()
    $line.name = 'line'
    $line.zIndex = 2
    Line.render($line, closePaths, {color: INACTIVE_AREA})
    //渲染面
    const $polygon = new Graphics()
    Polygon.render($polygon, closePaths, {color: INACTIVE_AREA})
    //存储原始path坐标
    // $polygon.originalPaths = paths
    $polygon.name = 'polygon'
    $polygon.interactive = true
    $polygon.zIndex = 1
    $con.addChild($line)
    $con.addChild($polygon)

    //拖拽面更新
    const dragAreaUpdate = () => {
      $polygon.dragging = false
      $polygon.clickPos = null
      Control.enableDrag(true)
      if ($polygon.isUpdate) {
        this._updateFinished($con)
        $polygon.isUpdate = false
      }
    }

    const saveDragStartPos = () => {
      const $points = this.pointCtrl().sort($con)
      this.dragStartPos = $points.map($p => {
        const {x,y} = $p
        return {x,y}
      })
    }
    //面拖拽逻辑
    $polygon
      .on('pointerdown', function (e) {
        if (Event.activeKey !== 'Alt') return this.dragging = false;
        Control.enableDrag(false)
        this.clickPos = e.data.getLocalPosition($con)
        saveDragStartPos()
        this.dragging = true;
      })
      .on('pointerup', (e) => {
        dragAreaUpdate()
      })
      .on('pointerupoutside', (e) => {
        dragAreaUpdate()
      })
      .on('pointermove', (e) => {
        const {dragging, clickPos} = $polygon
        if (dragging) {
          // this.areaInnerMarkerCtrl().clear($con)
          const {x: cx, y: cy} = clickPos
          const {x: mx, y: my} = e.data.getLocalPosition($con);
          //位移量
          const tx = mx - cx
          const ty = my - cy
          //离点击点的相对距离
          const movePos = {x: tx, y: ty}
          this._dragUpdatePolygon($polygon.parent, movePos)
          $polygon.isUpdate = true
        }
      })
    //渲染控制点
    this.pointCtrl().renderActivePoints($con,paths)
    //渲染未激活点
    if(!$con.isRect){
      this.pointCtrl().renderNoActivePoints($con,paths)
    }
    this._sortPoint($con)
    return $con
  }
  //给所有点重新排序，重新赋值index
  static _sortPoint($el) {
    const isRect = $el.isRect
    const $points = this.pointCtrl().sort($el)
    const sortPath = $points.map(($p,index) => {
      const {x,y,name} = $p
      if(!isRect) $p.pIndex = index
      return {x,y,pIndex:index,name}
    })
    return sortPath
  }
  //拖拽更新
  static _dragUpdatePolygon($el, movePos) {
    const $points = this.pointCtrl().sort($el)
    const {x: tx, y: ty} = movePos
    const paths = this.dragStartPos.map((p, index) => {
      const {x, y} = p
      const newPos = {x: x + tx, y: y + ty}
      Object.assign($points[index], newPos)
      return newPos
    })
    this._renderPolygon($el, paths)
  }

  //更新完成
  static _updateFinished($el) {
    const isRect = $el.isRect
    let cadPaths
    const sortPath = this._sortPoint($el)
    const newPaths = sortPath.filter(item => item.name === 'point')
    cadPaths = newPaths.map(p => {
      const {x, y} = p
      return pixi2cad({x, y})
    })
    LayerManager.updateElements({
      id: 'AREA',
      data: [{
        id: $el.id,
        controlPoints: cadPaths
      }]
    })
    EventBus.$emit('AREA:UPDATED')
  }

  //更新多边形面
  static _updateEditPolygon($el) {
    const sortPath = this._sortPoint($el)
    let paths = sortPath.filter(item => item.name === 'point')
    paths = paths.map(p => {
      const {x, y} = p
      return {x, y}
    })
    this._renderPolygon($el, paths)
    //存储原始点位数据
    // $polygon.originalPaths = paths
  }

  //更新矩形面
  static _updateEditRect($el, pIndex) {
    //获取相邻点的index
    const getAdjacentPointIndex = (pIndex) => {
      if (pIndex === 0) {
        return [1, 3]
      } else if (pIndex === 1) {
        return [0, 2]
      } else if (pIndex === 2) {
        return [3, 1]
      } else if (pIndex === 3) {
        return [2, 0]
      }
    }
    const $points = $el.children.filter(child => child.name === 'point')
    // const $polygon = $el.getChildByName('polygon')
    const adjacentIndex = getAdjacentPointIndex(pIndex)
    const {x: mx, y: my} = $points[pIndex]
    $points[adjacentIndex[0]].y = my
    $points[adjacentIndex[1]].x = mx
    const paths = $points.map(p => {
      const {x, y} = p
      return {x, y}
    })
    this._renderPolygon($el, paths)
    //存储原始点位数据
    // $polygon.originalPaths = paths
  }
}
