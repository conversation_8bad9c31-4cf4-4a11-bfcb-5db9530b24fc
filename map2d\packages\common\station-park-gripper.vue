<template>
  <div class="park-item-gripper">
    <div class="gripper-info">
      <gripper-icon />
      <span v-if="!modelCategory || modelCategory !== 'PP_LITE'" class="title-label">
        {{ $t(parkPositionDict[parkPosition]) }}
      </span>
    </div>

    <div class="gripper-lattice">
      <el-tooltip :content="tooltipContent" placement="top">
        <lattice-item
          type="gripper"
          :lattice="lattice"
          :stationError="parkError"
          :isActiveLattice="
            currentSelected?.type === 'lattice' &&
            currentSelected.lattice?.latticeCode === lattice.latticeCode
          "
          :isActiveBox="
            currentSelected?.type === 'box' &&
            currentSelected.lattice?.relateBoxCode === lattice.relateBoxCode
          "
          @select="clickHandler"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import gripperIcon from "./icon/gripper-icon.vue";
import LatticeItem from "./lattice-item.vue";

export default {
  name: "Map2DRightParkItemGripper",
  components: { gripperIcon, LatticeItem },
  props: ["errorKeys", "parkPosition", "gripperRack", "currentSelected", "modelCategory"],
  data() {
    return {
      parkPositionDict: {
        LEFT: "lang.rms.fed.leftSide",
        RIGHT: "lang.rms.fed.rightSide",
      },
    };
  },
  computed: {
    lattice() {
      return this.gripperRack?.lattices[0] || {};
    },

    parkError() {
      const lattice = this.lattice;
      const latticeCode = lattice?.latticeCode || "";
      const relateBoxCode = lattice?.relateBoxCode || "";

      return this.errorKeys[latticeCode] || this.errorKeys[relateBoxCode];
    },

    tooltipContent() {
      const lattice = this.lattice;
      const errorKeys = this.errorKeys;
      const parkError = this.parkError;

      const relateBoxCode = lattice?.relateBoxCode;
      const latticeCode = lattice?.latticeCode;

      let status = "";
      if (relateBoxCode) {
        // 货箱无任务
        if (parkError) {
          status = errorKeys[relateBoxCode];
          // 任务中
        } else if (lattice?.relateBox?.jobIds?.length || lattice?.relateBox?.goFetchJobs?.length) {
          status = "lang.rms.web.monitor.robot.working";
        }
      } else {
        if (lattice?.latticeStatus === "ALLOCATED" && lattice.latticeFlag === "NORMAL") {
          status = "lang.rms.fed.preOccupied";
        } else if (parkError) {
          status = errorKeys[latticeCode];
        }
      }
      if (status) return this.$t(status) + " - " + (relateBoxCode || latticeCode);
      else return relateBoxCode || latticeCode;
    },
  },
  methods: {
    clickHandler() {
      this.$emit("click", this.lattice);
    },
  },
};
</script>

<style lang="less" scoped>
.park-item-gripper {
  position: relative;
  .g-flex();
  height: 28px;
  line-height: 28px;

  .gripper-info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex: 0 0 50%;

    .title-label {
      font-size: 12px;
    }
  }

  .gripper-lattice {
    display: flex;
    flex: 0 0 50%;
  }
}
</style>
