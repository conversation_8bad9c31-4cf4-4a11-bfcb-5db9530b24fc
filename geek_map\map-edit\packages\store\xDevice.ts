import { defineStore } from "pinia";
import mapVNode from "@packages/hook/useMapVNode";
// import { MapDataConf } from "@packages/type/listener";

type Code = string | number;
export interface DmsDevice {
  cellCode: string;
  deviceCode: Code; // 这里可能是各种元素集合
  floorId: string | number;
  mapId: string | number;
  deviceType: string;
  devicePointType: string;
  plcCode: string | number;
  parentDeviceCode: string | number | null;
  id?: number;
  deviceName?: string;
  location?: any;
  bcrCode?: Code | undefined;
  weighingSensorCode?: Code | undefined;
  sizeValidatorSensorCode?: Code | undefined;
  direction?: string;
  stationId?: Code;
  needDelete?: boolean;
}

// 这里存储可map中可用的模板替换数据
export const useDmsDeviceStore = defineStore({
  id: "xdevice",
  state: (): {
    // 所有的dmsDevice数据
    dmsDeviceDtoList: DmsDevice[];
  } => {
    return {
      dmsDeviceDtoList: [],
    };
  },
  actions: {
    getDmsDeviceDtoList() {
      return this.dmsDeviceDtoList;
    },

    //初始化
    setDmsDeviceDtoList(list: DmsDevice[]) {
      console.log(" 🍃  🍃  🍃 xdevice 初始化 🍃  🍃  🍃 ", list);
      this.dmsDeviceDtoList = list;
    },
  },
});
