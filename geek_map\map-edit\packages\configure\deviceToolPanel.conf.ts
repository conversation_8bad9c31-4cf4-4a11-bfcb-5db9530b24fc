import { ToolPanelType } from "@packages/type/editUiType";

/**
 * EditDevice文案(PPP 工作站设备编辑)
 */
const TITLE_CONF: ToolPanelType = {
  option: {
    title: "Edit Device",
    name: "title",
    className: "editMapToolTitleStyle",
  },
};

/**
 * 居中
 */
const CENTER_CONF: ToolPanelType = {
  option: {
    icon: "map-font-zhongxin",
    title: "lang.rms.fed.center",
    name: "center",
    describe: "lang.rms.fed.mapCenter",
    eventName: "map:center",
  },
};

/**
 * 顶部按钮组数据
 */
export const toolPanelList: ToolPanelType = {
  config: {
    border: true,
    defActive: true,
  },
  children: [TITLE_CONF, CENTER_CONF],
};
