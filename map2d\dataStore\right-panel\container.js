import { defineStore } from "pinia";
import { getMap2D } from "@map2d/singleton/index";

export const useMap2dContainerStore = defineStore("map2dContainerStore", {
  state: () => ({
    containerType: "", // shelf | rack | xShelf | poppick
    containerCode: "", // 容器编号
    radioValue: "single", // single, multi, rect
    multiRackCodes: [], // 多选货箱架Code
    /**
     * search-box 搜索出的数据
     * { containerType, containerCode, queryType, queryCode }
     */
    searchData: null,

    /**
     * 当前 货箱架 选中的货箱/货位 信息
     * { type: "lattice", lattices: data[] } | { type: "box", lattice:data}
     */
    rackCurrentSelected: null,
    /**
     * 目标 货箱架/机器人 选中的格子信息
     * {type: "rack", latticeCode} | {type: "robot", robotId, latticeCode}
     */
    rackTargetSelected: null,

    /**
     * 当前 ppp货架 选中的货箱/货位 信息
     * { type: "lattice", lattices: data[] } | { type: "box", lattice:data}
     */
    pppCurrentSelected: null,
    /**
     * 目标 ppp货架/工作站 选中的格子信息
     * {type: "poppick", latticeCode} | {type: "station", latticeCode}
     */
    pppTargetSelected: null,

    /**
     * 异常货箱数据
     */
    boxExceptionRackCodeList: [],
    /**
     * 异常货位数据
     */
    latticeExceptionRackCodeList: [],
    rightContainerRobot: {}, // 右侧容器面板搜到的机器人信息
  }),
  actions: {
    setContainerType(type) {
      if (type === this.containerType) return;
      this.containerType = type;
    },
    setContainerCode(code) {
      if (code === this.containerCode) return;
      this.containerCode = code;
    },
    setRadioValue(value) {
      if (value === this.radioValue) return;
      this.radioValue = value;
    },
    setMultiRackCodes(codes) {
      this.multiRackCodes = codes;
    },
    setSearchData(data) {
      this.searchData = data;
    },
    setRackCurrentSelected(data) {
      this.rackCurrentSelected = data;
    },
    setRackTargetSelected(data) {
      this.rackTargetSelected = data;
    },
    setPppCurrentSelected(data) {
      this.pppCurrentSelected = data;
    },
    setPppTargetSelected(data) {
      this.pppTargetSelected = data;
    },
    setBoxExceptionRackCodeList(data) {
      this.boxExceptionRackCodeList = data;
    },
    setLatticeExceptionRackCodeList(data) {
      this.latticeExceptionRackCodeList = data;
    },
    setRightContainerRobot(data) {
      this.rightContainerRobot = data;
    },

    resetContainerStoreData() {
      this.$reset();
    },
    /**
     * 查询货架
     * @param {String} code
     * @param {Function} cb
     */
    querySearch(code, cb) {
      if (!code) {
        cb([]);
        return;
      }

      const map2D = getMap2D();
      let msgType = "QueryInstructionRequestMsg";
      map2D.mapWorker
        .wsRequest(msgType, {
          instruction: "SHELF",
          queryCode: code,
        })
        .then(res => {
          const queryList = res?.body || [];

          if (queryList[0]?.robotId) {
            // 这里查询到的是机器人
            const item = queryList[0];
            cb([
              {
                containerType: "robot",
                containerCode: item.robotId,
                queryType: item?.codeResultType ? item.codeResultType.toLowerCase() : "",
                queryCode: item.queryCode,
                robotLayer: item.robotLayer,
                label: item.robotId,
                value: item.robotId,
              },
            ]);
          } else {
            const results = queryList
              .map(item => {
                const containerCode = item.parentContainerCode || item.rackCode;
                if (!containerCode) return null; // 无 containerCode 则返回 null

                return {
                  containerType: item?.shelfType?.toLowerCase() || "", // 处理可能的 undefined
                  containerCode,
                  queryType: (item?.codeResultType || "").toLowerCase(), // 简化处理并避免报错
                  queryCode: item.queryCode,
                  label: containerCode,
                  value: containerCode,
                };
              })
              .filter(Boolean); // 过滤掉 null 或 undefined 的项

            cb(results);
          }
        });
    },
  },
});
