<template>
  <rms-empty-data v-if="!totalSize" />
  <div
    v-else
    @scroll.stop="virtualScroll()"
    ref="listBox"
    class="map-abnormal-list"
    :class="{ 'data-empty': !totalSize }"
  >
    <div
      class="list"
      :style="{
        'padding-top': `${listPaddingTop}px`,
        'padding-bottom': `${listPaddingBottom}px`,
      }"
    >
      <div
        v-for="(item, index) in visibleItems"
        :key="index"
        class="list-item"
        :class="{ 'is-danger': item.errorLevel == 3, 'is-warn': item.errorLevel == 2 }"
      >
        <p @click.stop="showDetailInfo(item)">
          <strong>{{ item.eventObj }}</strong>
          <span>{{ item._langText }}</span>
        </p>

        <el-icon v-show="[1, 5, 7].includes(item.category)" :size="14" @click.stop="located(item)">
          <gp-icon-aim />
        </el-icon>
      </div>
    </div>

    <abnormal-dialog ref="abnormalDialog" />
  </div>
</template>

<script>
import { debounce } from "throttle-debounce";
import { mapState } from "pinia";
import { useMap2dAbnormalStore } from "@map2d/dataStore/top-panel/panel-abnormal";
import { getMap2D } from "@map2d/singleton";

import AbnormalDialog from "./abnormal-dialog.vue";
import RmsEmptyData from "@plugins/pageComponents/rms-empty-data.vue";

export default {
  name: "Map2dAbnormalList",
  components: { AbnormalDialog, RmsEmptyData },
  data() {
    return {
      visibleCount: 30, // 可见元素个数
      startIndex: 0,
      lastScrollTop: undefined, // 用于存储上一次的滚动位置
      listPaddingTop: 0,
      listPaddingBottom: 0,

      endPaddingTop: 0,
    };
  },
  computed: {
    ...mapState(useMap2dAbnormalStore, ["wsEventMessageList"]),

    totalSize() {
      return this.wsEventMessageList.length;
    },

    visibleItems() {
      if (!this.totalSize) return [];
      const list = this.wsEventMessageList.slice(
        this.startIndex,
        this.startIndex + this.visibleCount,
      );

      return list.map(item => {
        item._langText = this.formatLang(item);
        return item;
      });
    },
  },

  methods: {
    // 滚动事件处理
    virtualScroll() {
      return debounce(100, () => {
        const $target = this.$refs.listBox;
        if (!$target) return;

        const scrollTop = $target.scrollTop; // 元素顶部已滚动的距离
        const distance = Math.abs(this.lastScrollTop ? scrollTop - this.lastScrollTop : 0);
        const scrollItemNum = distance / 28; // 滚动的条目数
        const scrollDirection = this._determineScrollDirection(scrollTop);
        // 根据滚动方向执行相应操作
        switch (scrollDirection) {
          case "up":
            // 处理向上滚动的情况
            const upIndex = this.startIndex - scrollItemNum;
            if (upIndex < 0) {
              this.startIndex = 0;
              this.listPaddingTop = 0;
            } else {
              this.startIndex = upIndex;
              this.listPaddingTop = scrollTop;
            }
            break;
          case "down":
            // 处理向下滚动的情况
            const downIndex = this.startIndex + scrollItemNum;
            const endIndex = downIndex + this.visibleCount;
            if (endIndex > this.totalSize) {
              const _i = this.totalSize - this.visibleCount;
              this.startIndex = _i > 0 ? _i : 0;
            } else {
              this.startIndex = downIndex;
              this.listPaddingTop = scrollTop;
            }
            break;
        }
      })();
    },

    _determineScrollDirection(currentScrollTop) {
      if (this.lastScrollTop === undefined) {
        this.lastScrollTop = currentScrollTop;
      }

      if (currentScrollTop > this.lastScrollTop) {
        // 向下滚动
        this.lastScrollTop = currentScrollTop;
        return "down";
      } else if (currentScrollTop < this.lastScrollTop) {
        // 向上滚动
        this.lastScrollTop = currentScrollTop;
        return "up";
      }
    },

    formatLang(data) {
      const { eventType, eventContent, faultCode } = data;

      if (!eventContent) {
        return "";
      }

      const transMsgLang = $utils.Tools.transMsgLang;

      if ([90005, 90001, 90002, 90003, 90004].includes(faultCode)) {
        return transMsgLang(eventType) + transMsgLang(eventContent);
      } else {
        return transMsgLang(eventContent);
      }
    },

    showDetailInfo(item) {
      this.$refs?.abnormalDialog?.open(item);
    },

    located(item) {
      const { mapRender } = getMap2D();
      if (!mapRender) return;

      let curType;
      switch (item.category) {
        case 1:
          curType = "robot";
          break;
        case 5:
          curType = "charger";
          break;
        case 7:
          curType = "shelf";
          break;
      }
      mapRender.clearSelects();
      if (!curType) return;
      mapRender.setElementCenter(curType, item.eventObj);
      mapRender.select({ [curType]: [item.eventObj] });
    },
  },
};
</script>

<style lang="less" scoped>
.map-abnormal-list {
  position: relative;
  width: 0 0 100%;
  height: 360px;
  overflow-y: auto;
  &.data-empty {
    height: auto;
  }

  .list {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 26px;
      padding: 5px 0;
      border-bottom: 1px solid #d3d3d3;
      font-size: 12px;
      color: #666;

      &.is-danger {
        color: #f24141;
      }
      &.is-warn {
        color: #ff7f08;
      }

      > p {
        flex: 1;
        cursor: pointer;
        strong {
          font-weight: 600;
          padding-right: 2px;
        }
      }
      > .rms-icon {
        flex: 0 0 15px;
        cursor: pointer;
      }
    }
  }
}
</style>
