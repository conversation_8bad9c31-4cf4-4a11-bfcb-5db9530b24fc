<template>
  <el-icon :size="size">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        d="M5.33333 2H2.66667C2.29848 2 2 2.29848 2 2.66667V5.33333"
        :stroke="color"
        stroke-width="1.33333"
        stroke-linejoin="round"
      />
      <path
        d="M5.33333 14.0001H2.66667C2.29848 14.0001 2 13.7016 2 13.3334V10.6667"
        :stroke="color"
        stroke-width="1.33333"
        stroke-linejoin="round"
      />
      <path
        d="M10.6665 14.0001H13.3332C13.7014 14.0001 13.9998 13.7016 13.9998 13.3334V10.6667"
        :stroke="color"
        stroke-width="1.33333"
        stroke-linejoin="round"
      />
      <path
        d="M10.6665 2H13.3332C13.7014 2 13.9998 2.29848 13.9998 2.66667V5.33333"
        :stroke="color"
        stroke-width="1.33333"
        stroke-linejoin="round"
      />
      <path d="M8 5.33325V10.6666" :stroke="color" stroke-width="1.33333" stroke-linejoin="round" />
      <path
        d="M10.6665 5.33325V10.6666"
        :stroke="color"
        stroke-width="1.33333"
        stroke-linejoin="round"
      />
      <path
        d="M5.3335 5.33325V10.6666"
        :stroke="color"
        stroke-width="1.33333"
        stroke-linejoin="round"
      />
    </svg>
  </el-icon>
</template>

<script>
export default {
  name: "ScanIcon",
  props: {
    size: {
      type: Number,
      default: 16,
    },
    color: {
      type: String,
      default: "#86909C",
    },
  },
};
</script>
