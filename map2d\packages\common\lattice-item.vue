<template>
  <span v-if="empty" class="lattice-item no-data" />
  <span
    v-else
    class="lattice-item"
    :class="{
      'is-outer': type == 'rack' && lattice?.isOuter,
      'has-box': lattice.relateBoxCode || lattice?.relateBox?.boxCode,
      'lattice-active': isActiveLattice,
      'box-active': isActiveBox,
      'lattice-allocated': lattice?.latticeStatus == 'ALLOCATED',
      'lattice-locked': lattice?.latticeFlag == 'LOCKED',
      'box-locked': lattice?.relateBox?.lockState === 1,
      'has-job': lattice?.relateBox?.jobIds?.length || lattice?.relateBox?.goFetchJobs?.length,
      'box-confirm':
        lattice.boxStatus === 'POSITION_CONFIRMING' ||
        lattice?.relateBox?.boxStatus === 'POSITION_CONFIRMING',
      'locked-abnormal':
        lattice?.latticeFlag == 'LOCKED' &&
        lattice?.lockReason &&
        lattice?.feLatticeFlag &&
        lattice?.feLatticeFlag !== 'MANUAL_LOCK',
      'locked-exception': lattice?.feLatticeFlag === 'SYSTEM_LOCK' && type === 'rack',
      'station-error': ['gripper', 'virtual'].includes(type) && stationError,
    }"
    :title="domTitle"
    @click.stop="$emit('select')"
  >
    <i v-if="!['gripper', 'virtual'].includes(type)" class="icon-box" />
    <i v-if="!['gripper', 'virtual'].includes(type)" class="icon-hasJobIds" />
    <slot name="icon" />
    {{ boxCode }}
  </span>
</template>

<script>
export default {
  name: "lattice-item",
  props: {
    type: {
      type: String,
      default: "rack", // rack | ppp | robot | gripper | virtual
    },
    // lattice数据
    lattice: {
      type: Object,
      default: () => {},
    },
    // lattice选中状态
    isActiveLattice: {
      type: Boolean,
      default: false,
    },
    // box选中状态
    isActiveBox: {
      type: Boolean,
      default: false,
    },

    // 无数据时展示
    empty: {
      type: Boolean,
      default: false,
    },
    stationError: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    boxCode() {
      if (this.lattice?.relateBoxCode) {
        return this.lattice.relateBoxCode;
      } else if (this.lattice?.relateBox?.boxCode) {
        return this.lattice.relateBox.boxCode;
      } else {
        return this.lattice.latticeCode;
      }
    },

    domTitle() {
      if (["gripper", "virtual"].includes(this.type)) return "";
      else return this.lattice.latticeCode || "";
    },
  },
};
</script>

<style lang="less" scoped>
.lattice-item {
  position: relative;
  flex: 1;
  border: 1px solid #a8d7ff;
  cursor: pointer;
  margin: 0 1px;
  padding: 0 3px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 24px;
  font-size: 12px;
  line-height: 24px;
  background: #fff;

  &::after {
    position: absolute;
    content: " ";
    width: 0;
    height: 0;
    line-height: 0;
    font-size: 0;
    right: 0;
    bottom: 0;
    border: 6px solid transparent;
    border-right-color: #049252;
    border-bottom-color: #049252;
    display: none;
    z-index: 999;
  }

  &.no-data {
    cursor: default !important;
    background: none !important;
    border: 0;
  }

  &.has-box {
    background: #d0f9e6;
    color: #198353;
    text-align: left;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .icon-box {
      display: inline-flex;
      width: 11px;
      height: 12px;
      background: url(data:image/png;base64,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)
        0 100% no-repeat;
      background-size: 10px;
    }
  }

  // 被分配
  &.lattice-allocated {
    background-color: #a8d7ff;
    color: #002966;
    // 蓝箱子
    .icon-box {
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAdpSURBVHgB7ZrLbxvXGcXPcEhKlMSXKJF6m3TtJrbjNEaLPhYFXBjd1YbzDzTRH1DYXietKbhBl7FRoFvL3bcO4kUXhdEUCZCNETiOg0SJE0qWRJEiKT6Hr3ncfHfIYShZkvl2EM3PoDUkh0N+537n3DvkACYmJiYmJiZHFQEDJnw34hGt9isAu8yYdusvlxaW8QIZqADh959etsDyrspYUNU02K0iGPCQKfLr4ddDq3gBDESA8N3186JVuE7Fnk9mi4jtFJBKZ+F1OXByLgDHsI0+ibCsydWlQQvRVwF4u1tE23V6l6uFUhXriRyqsgLGGDLZAkqVCmzUBcemfTg57+cirAoMy3++OLuEAdE3AW7c26QRZ1eriurZoMILxSo0Kpy8D/4nkyugTAIYOIZsOEEizPm9uhDQ1KVB5EPPBeDtbhGF29znvN3jaQm8Yr14unEJ+HYuL+0SwGDcNYpXT8zVbAHhPU2pXuunLXomALV7ULTabnOfp7IlxDMSFFVFrW7WEIELwLdzBS5A9cDjzfo9A8mHrgVo9rlUlrFNI14oV/Wied2jDjscdhviOzm9eEOMnFRE5RABONwWwZkJBCkj+mWLrgS4cS96hcY1zH0eS0nIlSpgGtOLtFK4zQfcCM67USoq+PCTVejjz2rP57kAVbml99mbD5qski0W3kMP6EgA7nPBKryraey1VK6IVK4MVdX0AgX6F/CN4fi8Bx63nR4HPv0ijrVo+nsB6BiFNgQw6Ict2hKg2ef5YgVbOxJNa2q9MEAULTh7MoCAfwRWUUChIOPRV3HEkvnGyGu1ENAF4FNiJ3BLBKcndCFI8LCqVO90KkTLArz9r7XrdrvIfe7ZpoCTSrXRM4rnARec8eLUCR+NrIInazt4upWBLNc6w8gEVp8KpWKpYwE4z9hCI1tcat8W1lZ3/DLyNDzr98HpHIVdFCFBbhSGemEShd/TzRxWVhOU8HKtaHotqw17XQTWeLwbZPIWf4+qokCV1WCpWqHzC/RPgCr5NbIRg50S/eXQHMbdDqzFMihXa6PIi4rRgmeLbkaRteZguzqg9h/QjQI8C04HZyhfFEhSCRqdV3R6wJYFMOBCPFqJwOd14fiUj6Y8hbIgr7c9q/sbzcWzpvZvtgLahy+S+JLZOTKEYqkEWencQgZtC2CQSuf05WzA58XJWR/4bLCZzNXbvRZ2rLH6a/Y/UCu/dQm433nhM5MePTv4IqpXtCUAH3XXqAOb8R0KMFmf+qLbKSQzOfB8OHt8CpuJLN2X6q1ez4B68c8K8ZwPZ7UgRGm/MD3OfY50Nt/ya1ul7Q6IPPgPQq/+FhXBhWg8pT9m5IOTxAnNTcHrdGB1a0fPh73B15wPhxEYd+IU+VyfThs+7z1tC5BPJ5BcX4Fz3I8QTUF5WUSS7KA/Rx/UyIczQT8yUgXr8TSd9srPCHDQPGD43D02TO1eJq937/PDsLSzMx9pPvpDw6PI72zj0f//jUrsC/hGdx+G58OXkQ0wVcaZ49Pwe53Pjv6e+nm7nwpN45dnghi2iXq+NIecaLXCZrOj17TVAXyEK3Y3Zin0nKWU3g3Rbz+DPb6BoYVf7dqXi7W+lUA8ldHzYWHqGJ5sJJBI5xszggEfcb6yU2ha2+tziyDA5R6D3z9Gp9BlxLaq6CUdTYPGeuCl31xCdOVjJGObz92f2+KncxPwuUcQ2UzRClFunPtzn+claZfPBSp8lDLF73fC5bTTc7TOiFXQazqeBnlhK5F1+AKn8bOXfq23/GFwW/Cbf8KLX7w8j7KiYmzYrgfcXp9bLBYEAh74J0cgWngIyojSsrpQKKPXdCyAgVFYq2wn07q/56cmka7sP6Iu6ozJiRESWUU8nqfvD4s/nFmgF/DuyeYL+rS5HzwBthMSkrSwUqhT+skLEeB5ZDN5ZDEY2poGf4yYAuCIYwqAI44pAI44pgA44pgCtLwnYwO7aKFtGDJ0qnQHHdCyAO+/dSFMX06E0OEb9QUqXGDakqBVQ3//4yvL6ICOfhz9w43/nrdYxdv0AYIH7VPe+hxW9zSsI+P7Pj9BX5AcdDbYEgL7QFDExZuLp1bRBV39PH7xnftvChbh+n5CVJLfQPr6I4wc+zmGJn8Cwba72I4FYHgoaNq1m4tnP0AP6CoE7711YdkC4XfYxxYWqwPlxDdgJLEql1Gh7a7g7Q527dYbZ871qnhOzy6Rufy3/wXpF+Iwbb7B75ejj6FR4Tb3DAS7A0omCrWY1m3BO6KdDhAYu0UHC99cPJdBj+n5RVLNtiiuPdCL5d/wlDcfo/DkQ7hO/x72yROYmj32fAF0n4Pa/ZWH6BN9u0zu4l/vXxVE4YoqpYMW2zBZ4FvovxlSV9hJlEMFYMKqoKmLvWz1g+jbQuje2xdu8nwQR7x3eADaPDMQaUag+we/qDGtVc4NonjOQC6VrefDXdp8TaYssI56MekP7O2AZUGtXuuHzw9joBdLN+dDIwR1n7OlQY34D4JL79wPL/7jQeRP/3z8JkxMTExMTExMXhDfAdyNP2wbuGZMAAAAAElFTkSuQmCC)
        0 100% no-repeat;
      background-size: 10px;
    }
  }

  // 待确认
  &.box-confirm {
    background-color: #8277ff;
    color: #fff;

    .icon-box {
      display: inline-flex;
      width: 11px;
      height: 12px;
      background: url(data:image/png;base64,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)
        0 100% no-repeat;
      background-size: 10px;
    }
  }

  &.lattice-locked,
  &.box-locked {
    background: #c8cdd3;
    color: #4b5668;
  }

  // 锁定异常
  &.locked-abnormal {
    background: #f24141;
    color: #fff;
  }

  &.locked-exception {
    background: #fad7d7;
  }

  .icon-hasJobIds {
    display: none;
  }
  &.has-job {
    // background-color: #269bff;
    // color: #fff;

    .icon-hasJobIds {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 10px;
      line-height: 10px;
      color: #666 !important;
      display: inline-flex;
      width: 11px;
      height: 12px;
      background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjgyMzA0Njk4Mzg1IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjEwMTQiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMiA1MTJtLTI1NiAwYTI1NiAyNTYgMCAxIDAgNTEyIDAgMjU2IDI1NiAwIDEgMC01MTIgMFoiIGZpbGw9IiMzQkQ1QjMiIHAtaWQ9IjEwMTUiPjwvcGF0aD48cGF0aCBkPSJNNTEyIDI1NnYyNTZsLTE4MS4xMiAxODEuMTJBMjU2IDI1NiAwIDEgMCA1MTIgMjU2eiIgZmlsbD0iIzRBOEJGRSIgcC1pZD0iMTAxNiI+PC9wYXRoPjxwYXRoIGQ9Ik01MTIgOTI4YTQxNiA0MTYgMCAxIDEgNDE2LTQxNiA0MTYgNDE2IDAgMCAxLTQxNiA0MTZ6IG0wLTc2OGEzNTIgMzUyIDAgMSAwIDM1MiAzNTJBMzUyIDM1MiAwIDAgMCA1MTIgMTYweiIgZmlsbD0iIzNCRDVCMyIgcC1pZD0iMTAxNyI+PC9wYXRoPjxwYXRoIGQ9Ik01MTIgOTI4YTQxMi40OCA0MTIuNDggMCAwIDEtMjk0LjA4LTEyMS45Mmw0NS4xMi00NS4xMkEzNTIgMzUyIDAgMSAwIDUxMiAxNjBWOTZhNDE2IDQxNiAwIDAgMSAwIDgzMnoiIGZpbGw9IiM0QThCRkUiIHAtaWQ9IjEwMTgiPjwvcGF0aD48L3N2Zz4=)
        0 100% no-repeat;
      background-size: 10px;
    }
  }

  &.lattice-active,
  &.box-active {
    border-color: #049252 !important;
    &::after {
      display: inline-block;
    }
  }

  &.station-error {
    color: #f24141;
  }
}
</style>
