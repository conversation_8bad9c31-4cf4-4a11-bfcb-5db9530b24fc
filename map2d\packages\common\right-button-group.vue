<template>
  <div class="map2d-right-btn-group">
    <template v-for="(item, index) in permissionList" :key="index">
      <el-button
        v-show="!item.hidden"
        size="small"
        type="primary"
        @click="controlHandler(item)"
        class="component-button-item"
        :class="{ dark: item.dark, 'long-btn': item.longBtn }"
      >
        {{ $t(item.label) }}
      </el-button>
    </template>
  </div>
</template>

<script>
import { mapState } from "pinia";
import { useRootMenuStore } from "@stores/rootMenuStore";

export default {
  name: "Map2dRightBtnGroup",
  props: {
    list: {
      type: [Array, Object],
      default: () => [],
    },
  },
  computed: {
    ...mapState(useRootMenuStore, ["getBtnPermission"]),

    permissionList() {
      let list = this.list;

      if (Object.prototype.toString.call(list) === "[object Object]") {
        list = Object.values(list);
      }

      let arr = [];
      list.forEach(item => {
        const permissionCode = item.permissionCode;
        if (permissionCode && !this.getBtnPermission(permissionCode)) return;
        arr.push(item);
      });
      return arr;
    },
  },
  methods: {
    controlHandler(item) {
      this.$emit("controlHandler", item);
    },
  },
};
</script>

<style lang="less" scoped>
.map2d-right-btn-group {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding-bottom: 8px;
  margin-top: -8px;

  > .component-button-item {
    width: 48%;
    letter-spacing: 1px;
    margin: 8px 0 0;
    padding: 6px 8px;
    height: unset;
    min-height: 28px;
    font-size: 12px;
    border-radius: 2px;
    text-align: center;
    word-break: break-word;
    white-space: unset;
    background: linear-gradient(180deg, #26b1ff 0%, #0b89f4 100%);
    box-shadow:
      0 1px 0 0 rgba(255, 255, 255, 0.3) inset,
      0 2px 3.5px 0 rgba(0, 0, 0, 0.13);
    overflow: hidden;

    &.dark {
      background: linear-gradient(180deg, #002966 0%, #004d9c 100%);
    }
    &.long-btn {
      width: 100%;
    }
  }
}
</style>
