import "@packages/public-path";
import { createApp } from "vue";
import App from "@packages/App.vue";
import router from "@packages/router";
import { setupI18n } from "@packages/logics/i18n";
import { setupTour } from "@packages/logics/tour";
import { setupComponents } from "@packages/logics/components";
import { setupStore } from "@packages/logics/store";
import { setupIframeListener } from "@packages/logics/iframeListener";
import { setupElementPlus } from "@packages/logics/elementplus";

import "geekplus-ui/lib/theme-chalk/index.css";

import GeekplusUI from "geekplus-ui";

//虚拟滚动条
// import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
// import { RecycleScroller } from 'vue-virtual-scroller'
// @style
import "vue-tour/dist/vue-tour.css";
import "element-plus/dist/index.css";
import "@packages/style/index.scss";
import "@packages/assets/iconFont/iconfont.css";

let app: any = null;
function render(props: { container: Element | undefined }) {
  const { container } = props;
  /**
   * 顺序不要错, 一定要先注册store,
   * 因为后续可能会在初始化的时候在store中存一些数据
   */
  app = createApp(App);
  // 注册路由
  app.use(router);
  //虚拟滚动条
  // app.component('RecycleScroller', RecycleScroller)
  // 注册 sotre
  setupStore(app);

  // 注册国际化
  setupI18n(app);

  // 注册用户引导
  // setupTour(app);

  // 注册iframe方式
  setupIframeListener();

  // 注册全局组件
  setupComponents(app);

  // 注册element
  setupElementPlus(app);

  app.use(GeekplusUI, {
    size: "middle",
    namespace: "rms",
  });

  const getInput = (el: HTMLElement): HTMLInputElement | null =>
    el instanceof HTMLInputElement ? el : el.querySelector("input");
  let inputHandler = () => {};
  app.directive("numberOnly", {
    mounted(el: HTMLElement, { arg, value }: any) {
      const input: HTMLInputElement = <HTMLInputElement>getInput(el);
      if (input) {
        // 小数正则
        const decimal: string = arg ? `(\\.\\d{0,${arg}})?` : "";
        // 整数正则
        const integer: string = value ? `(0|[1-9]\\d{0, ${value - 1}})` : "\\d*";
        const regExp: RegExp = new RegExp(integer + decimal, "g");
        inputHandler = () => {
          // 替换所有的非数字项
          // 如果输入的数字不符合正则表达式，则替换为''
          input.value =
            input.value
              .toString()
              .trim()
              .replace(/[^\d.]/g, "")
              ?.match?.(regExp)?.[0] ?? "";
        };
        // 在文本框输入的时候触发
        input.addEventListener("input", inputHandler, true);
      }
    },
    unmounted(el: HTMLElement) {
      // 解除绑定的时候去除事件
      const input: HTMLInputElement = <HTMLInputElement>getInput(el);
      input.removeEventListener("input", inputHandler, true);
    },
  });

  // qk兼容
  if (container) {
    app.mount(container.querySelector("#app") as Element);
  } else {
    app.mount("#app");
  }
}

// 独立运行时
if (!(window as any).__POWERED_BY_QIANKUN__) {
  render({ container: undefined });
}

/**
 * 下面是一些对qiankun的支持, 可忽略
 */
export async function bootstrap() {}

export async function mount(props: any) {
  render(props);
}

export async function unmount() {
  app.$destroy();
  app.$el.innerHTML = "";
  app = null;
}
