import { defineStore } from "pinia";
import { MapDataConf } from "@packages/type/listener";

/**
 * 模板可用keys
 */
const USABLE_KEYS: string[] = [
  "resolution",
  "locationX",
  "locationY",
  "locationY",
  "scale",
  "mapName",
  "mapId",
  "floorId",
  "status",
];

// 这里存储可map中可用的模板替换数据
export const useTemplateStore = defineStore({
  id: "mapData",
  state: (): MapDataConf => {
    const data: MapDataConf = {};
    USABLE_KEYS.forEach(key => {
      (data as any)[key] = undefined;
    });
    return data;
  },
  actions: {
    setItem(option: MapDataConf) {
      Object.keys(option).forEach(key => {
        USABLE_KEYS.includes(key) && ((this as any)[key] = (option as any)[key]);
      });
    },

    parseContent(content: string, defName?: string): string {
      if (!content.includes("{data:")) return content;
      let contentNew = content;
      USABLE_KEYS.forEach(key => {
        if (contentNew.includes(`{data:${key}}`)) {
          contentNew = contentNew.replaceAll(`{data:${key}}`, (this as any)[key] || defName || "");
        }
      });
      return contentNew;
    },
  },
});
