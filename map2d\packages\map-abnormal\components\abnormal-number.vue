<template>
  <div class="map-abnormal-number">
    <div class="title">
      <h6 @click="$emit('titleClick')">
        {{ $t(title) }}
      </h6>
      <!-- 按型号显示 分类数据 -->
      <el-switch
        v-if="showDetailSwitch"
        class="switch-btn"
        v-model="isShowDetail"
        inline
        size="small"
        inline-prompt
        :active-text="$t('lang.rms.fed.byRobotProduct')"
        :inactive-text="$t('lang.rms.fed.byRobotProduct')"
      />
      <div class="count-info">
        <div class="count-box">
          <label :title="$t('lang.rms.fed.totalCount')">
            {{ $t("lang.rms.fed.totalCount") }}
          </label>
          <span class="total-count">
            {{ totalCount || "-" }}
          </span>
        </div>

        <div class="count-box" v-if="!isHideExceptionCount">
          <label :title="$t('lang.rms.fed.textAbnormal')">
            {{ $t("lang.rms.fed.textAbnormal") }}
          </label>
          <span class="total-count red">
            {{ exceptionCount || "-" }}
          </span>
        </div>
      </div>
    </div>

    <div class="content" v-if="options.length">
      <div
        v-for="(item, index) in options"
        :key="index"
        class="option-item"
        :class="item.status"
        @click="$emit('statusClick', item)"
      >
        <label class="content-icon iconfont" :class="item.icon" :title="$t(item.label)" />
        <span class="name" :title="$t(item.label)">{{ $t(item.label) || "--" }}</span>
        <span class="value" :title="item.value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Map2dAbnormalNumber",
  props: {
    title: {
      type: String,
      default: "",
    },
    totalCount: [Number, String],
    exceptionCount: [Number, String],
    isHideExceptionCount: Boolean,
    options: {
      type: Array,
      default: [],
    },
    showDetailSwitch: {
      type: Boolean,
      default: false,
    },
    showRobotStatDetail: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShowDetail: false,
    };
  },
  watch: {
    isShowDetail(val) {
      this.$emit("showDetail", val);
    },
  },
  methods: {
    setDetailSwitch(val) {
      this.isShowDetail = val;
    },
  },
};
</script>

<style lang="less" scoped>
.map-abnormal-number {
  flex: 0 0 100%;

  .title {
    padding: 4px 0 2px;
    .g-flex();
    justify-content: none;

    > h6 {
      position: relative;
      padding-left: 6px;
      font-size: 12px;
      font-weight: 800;
      color: #4b5668;
      cursor: pointer;

      &::before {
        content: "";
        display: inline-block;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 12px;
        background-color: #2667ff;
      }
    }

    .switch-btn {
      :deep(.rms-switch__core.is-text:before) {
        line-height: 14px;
        height: 14px;
        padding: 0 4px 0 16px;
      }

      &.rms-switch.is-checked {
        :deep(.rms-switch__core.is-text:before) {
          line-height: 14px;
          height: 14px;
          padding: 0 16px 0 4px;
        }
      }

      :deep(.gp-switch__core:after) {
        transition: none;
      }
    }

    .count-info {
      margin-left: auto;
      .count-box {
        display: inline-flex;
        margin-left: 12px;
        align-items: center;
        font-size: 0;
        > label {
          font-size: 12px;
          font-weight: 400;
          margin-right: 4px;
          max-width: 60px;
          // 不换行
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #1d222c;
        }

        > .total-count {
          font-size: 14px;
          font-weight: 600;
          text-align: right;
          font-family: Helvetica Neue;
          color: #0084f4;
          &.red {
            color: #f24141;
          }
        }
      }
    }
  }

  .content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 2px;

    .option-item {
      display: flex;
      padding: 3px 1px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      flex: 1;
      overflow: hidden;
      box-shadow: 5.4px 0px 18px 0px rgba(34, 87, 188, 0.1);
      background: rgba(255, 255, 255, 0.6);
      cursor: pointer;

      > .content-icon {
        font-size: 20px;
        text-align: center;
        color: #86909c;
      }

      > .name,
      > .value {
        font-family: DIN Alternate;
        font-size: 13px;
        text-align: center;
        font-weight: none;
        color: rgb(38 155 255);
        padding-top: 1px;

        // 不换行
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      > .name {
        font-size: 12px;
        font-weight: 400;
        color: #1d222c;
      }

      &.work {
        border-bottom: 1px solid #269bff;
        .content-icon {
          color: #269bff;
        }
      }

      &.idle {
        border-bottom: 1px solid #00b44b;
        .content-icon {
          color: #00b44b;
        }
      }

      &.charging {
        border-bottom: 1px solid #5fd0fe;
        .content-icon {
          color: #5fd0fe;
        }
      }

      &.offline {
        border-bottom: 1px solid #cf47ff;
        .content-icon {
          color: #cf47ff;
        }
      }

      &.sleep {
        border-bottom: 1px solid #8d97a5;
        .content-icon {
          color: #8d97a5;
        }
      }

      &.abnormal {
        border-bottom: 1px solid #f24141;
        .content-icon {
          color: #f24141;
        }
      }

      &.remove {
        border-bottom: 1px solid #efbd32;
        .content-icon {
          color: #efbd32;
        }
      }
    }
  }
}
</style>
