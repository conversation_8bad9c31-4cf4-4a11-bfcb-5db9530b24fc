<template>
  <EditMap v-if="!isDeviceEdit" />
  <EditDevice v-else />
</template>
<script setup lang="ts">
import EditMap from "@packages/views/EditMap/EditMap.vue";
import EditDevice from "@packages/views/EditDevice/index.vue";
// vue
import { ref, Ref, onBeforeMount } from "vue";

let isDeviceEdit: Ref<boolean> = ref(false);
onBeforeMount(async () => {
  const params: string[] = location.search.replaceAll("?", "").split("&");
  console.log("设备编辑:params", params);
  for (let i = 0; i < params.length; i++) {
    const element = params[i];
    if (element.indexOf("isDeviceEdit") !== -1) {
      console.log("===设备编辑===", element, isDeviceEdit);
      isDeviceEdit.value = true;
    }
  }
});
</script>
<style lang="scss">
html,
body,
#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  border: none;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  overflow: hidden;
}
</style>
