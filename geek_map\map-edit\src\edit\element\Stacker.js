import { cad2pixi, isHoverNode, pixi2cad, toFixed } from "../utils/utils";
import DeviceEvent from "../event/DeviceEvent";
import Device from "./Device";
import LayerManager from "../layerManager/LayerManager";
import Control from "../control/Control";
export default class Stacker extends Device {
  static name = "stacker";
  // static update($el,data) {
  //   const {location} = data
  //   const pixiLocation = cad2pixi(location)
  //   const elAttr = {
  //     x:pixiLocation.x,
  //     y:pixiLocation.y
  //   }
  //   Object.assign($el,elAttr)
  // }
  //给元素绑定事件
  static bindEvent($el) {
    const { onDragEnd, onDragStart } = DeviceEvent;
    function dragMove(e) {
      const { selected, data, id } = this;
      if (selected && data) {
        const p = data.getLocalPosition(this.parent);
        const hoverNode = isHoverNode(p);
        if (hoverNode) {
          const { x, y, nodeId, cellCode } = hoverNode;
          const elAttr = { x, y, cellCode };
          if (!cellCode) elAttr.mapEditItemId = nodeId;
          Object.assign(this, elAttr);
        } else {
          const { x, y, nodeId, cellCode } = this;
          if (!this.originalData) this.originalData = { x, y, cellCode };
          const { x: nx, y: ny } = p;
          const elAttr = { x: nx, y: ny, cellCode: null, mapEditItemId: null };
          Object.assign(this, elAttr);
        }
      }
    }
    //更新充电桩
    const updateStacker = ($el, e) => {
      if (!$el.selected || !$el.dragging) {
        Control.enableDrag(true);
        return;
      }
      onDragEnd($el, e);
      //判断是否吸附
      if (!$el.cellCode && !$el.mapEditItemId) {
        const elAttr = { ...$el.originalData };
        Object.assign($el, elAttr);
        $el.originalData = null;
        return;
      }
      const { id, cellCode, x, y, mapEditItemId } = $el;
      // const {x,y} = e.data.getLocalPosition($el.parent)
      //将pixi坐标转为cad坐标
      const location = pixi2cad({ x: toFixed(x), y: toFixed(y) });
      const updateOp = {
        id: "STACKER",
        data: [{ id, cellCode, location, mapEditItemId: cellCode ? null : mapEditItemId }],
      };
      LayerManager.updateElements(updateOp);
      $el.originalData = null;
    };
    //   $el
    //     .on("pointerdown", function (e) {
    //       onDragStart(this, e);
    //     })
    //     .on("pointerup", function (e) {
    //       updateStacker(this, e);
    //     })
    //     .on("pointerupoutside", function (e) {
    //       updateStacker(this, e);
    //     })
    //     .on("pointermove", dragMove);
  }
}
