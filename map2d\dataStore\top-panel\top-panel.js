import { defineStore } from "pinia";
import { useMap2dStore } from "@map2d/dataStore/index";
import { getMap2D } from "@map2d/singleton/index";
/**
 * 顶部面板 - 操作按钮和快速查找
 */
export const useMap2dTopPanelStore = defineStore("useMap2dTopPanelStore", {
  state: () => ({
    clearFastSearchSelected: false, // 是否清除快速查找的 选中
    clearTool: "", // 需要被清除的工具
    showStationIds: false, //点击工作站ID,

    abnormalRackCodes: undefined, // 异常货架
    abnormalBoxCodes: undefined, // 异常货箱
    isAbnormalRackToolActive: $utils.Data.getMap2dToolStatus("abnormalRacks") || false, // 是否激活异常货架展示
  }),
  actions: {
    setClearTaskSelected(val) {
      this.clearFastSearchSelected = val;
    },
    setClearTool(val) {
      this.clearTool = val;
    },
    setShowStationIds(val) {
      this.showStationIds = val;
      this.updateStationIds();
    },

    updateStationIds() {
      const mapRender = getMap2D()?.mapRender;
      if (!mapRender) return;
      if (this.showStationIds) {
        mapRender.showScreenPositions(true, { type: "stations", codes: [] });
      } else {
        mapRender.showScreenPositions(false, { type: "stations", codes: null });
      }
    },

    setAbnormalRackCodes(codes) {
      const preCodes = this.abnormalRackCodes;
      if (preCodes == undefined && !codes?.length) return; // 首次进入，并且没有异常货架 不做处理

      if (preCodes == undefined && codes?.length) {
        // 首次进入，并且有异常货架
        const abnormalRacks = $utils.Data.getMap2dToolStatus("abnormalRacks") || false;
        abnormalRacks && getMap2D()?.mapRender?.toggle("rackAbnormal", true);
        this.abnormalRackCodes = codes;
        return;
      }

      if (preCodes?.join("") != codes?.join("")) {
        this.abnormalRackCodes = codes;
      }
    },

    setAbnormalBoxCodes(codes) {
      const preCodes = this.abnormalBoxCodes;
      if (preCodes == undefined && !codes?.length) return; // 首次进入，并且没有异常货架 不做处理
      if (preCodes == undefined && codes?.length) {
        this.abnormalBoxCodes = codes;
        return;
      }

      if (preCodes?.join("") != codes?.join("")) {
        this.abnormalBoxCodes = codes;
      }
    },

    setIsAbnormalRackToolActive(val) {
      if (val == this.isAbnormalRackToolActive) return;
      this.isAbnormalRackToolActive = val;
    },
  },
});
