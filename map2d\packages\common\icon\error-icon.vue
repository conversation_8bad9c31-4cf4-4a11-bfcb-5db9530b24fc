<template>
  <el-icon :size="size">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
      <g clip-path="url(#clip0_286_55042)">
        <path
          d="M15.6663 13.6186L9.38893 2.74667C8.62112 1.41745 7.3663 1.41745 6.59869 2.74667L0.3213 13.6186C-0.446308 14.9492 0.181677 16.0346 1.71582 16.0346H14.2717C15.8059 16.0345 16.4332 14.9491 15.6663 13.6186Z"
          :fill="color"
        />
        <path
          d="M7.44793 11.299H8.55245C8.68361 11.299 8.78716 11.1954 8.78716 11.0711L9.10471 4.63042C9.11852 4.49236 9.00116 4.375 8.8631 4.375H7.12348C6.98541 4.375 6.87496 4.49236 6.88186 4.63042L7.20632 11.0642C7.20632 11.1954 7.31677 11.299 7.44793 11.299Z"
          fill="white"
        />
        <path
          d="M6.99464 14.6585C6.72795 14.3918 6.57812 14.0301 6.57812 13.653C6.57812 13.2758 6.72795 12.9141 6.99464 12.6474C7.26133 12.3807 7.62304 12.2309 8.00019 12.2309C8.37735 12.2309 8.73906 12.3807 9.00575 12.6474C9.27244 12.9141 9.42226 13.2758 9.42226 13.653C9.42226 14.0301 9.27244 14.3918 9.00575 14.6585C8.73906 14.9252 8.37735 15.075 8.00019 15.075C7.62304 15.075 7.26133 14.9252 6.99464 14.6585Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_286_55042">
          <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
        </clipPath>
      </defs>
    </svg>
  </el-icon>
</template>

<script>
export default {
  name: "error-icon",
  props: {
    size: {
      type: Number,
      default: 16,
    },
    color: {
      type: String,
      default: "#FF4D3C",
    },
  },
};
</script>
