<template>
  <div class="map-abnormal-number-detail">
    <div class="detail-head" v-if="detailOptions.length">
      <div
        v-for="(item, index) in detailOptions"
        :key="index"
        class="option-item"
        :class="item.status"
      >
        <span class="name" :title="$t(item.label)">{{ $t(item.label) || "--" }}</span>
      </div>
    </div>

    <div class="content" v-for="(row, index) in detail" :key="index">
      <div class="robot-type">
        <span>{{ row.robotType }}</span>
      </div>
      <!-- dom 按照options固定顺序 排列  -->
      <div class="value-wrapper">
        <span class="value">
          {{ "totalCount" in row ? row.totalCount : "-" }}
        </span>
        <span class="value">
          {{ "workingCount" in row ? row.workingCount : "-" }}
        </span>
        <span class="value">
          {{ "idleCount" in row ? row.idleCount : "-" }}
        </span>
        <span class="value">
          {{ "chargingCount" in row ? row.chargingCount : "-" }}
        </span>
        <span class="value">
          {{ "exceptionCount" in row ? row.exceptionCount : "-" }}
        </span>
        <span class="value">
          {{ "disconnectedCount" in row ? row.disconnectedCount : "-" }}
        </span>
        <span class="value">
          {{ "sleepingCount" in row ? row.sleepingCount : "-" }}
        </span>
        <span class="value">
          {{ "removedCount" in row ? row.removedCount : "-" }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Map2dAbnormalNumber",
  props: {
    title: {
      type: String,
      default: "",
    },
    totalCount: [Number, String],
    exceptionCount: [Number, String],
    isHideExceptionCount: Boolean,
    options: {
      type: Array,
      default: [],
    },
    robotStat: {
      type: Object,
      default: () => ({}),
    },
    detail: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      detailOptions: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      const totalOptions = [
        {
          label: "lang.rms.fed.totalCount",
          status: "total",
        },
      ];

      this.detailOptions = totalOptions.concat(this.options);
    });
  },
};
</script>

<style lang="less" scoped>
.option-item {
  display: flex;
  padding: 4px 1px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  flex: 1;
  overflow: hidden;
  box-shadow: 5.4px 0px 18px 0px rgba(34, 87, 188, 0.1);
  background: rgba(255, 255, 255, 0.6);

  > .name {
    font-family: DIN Alternate;
    font-size: 14px;
    text-align: center;
    font-weight: 900;
    color: rgb(38 155 255);
    padding-top: 1px;

    // 不换行
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  > .name {
    font-size: 12px;
    font-weight: 400;
    color: #1d222c;
  }
}

.map-abnormal-number-detail {
  .detail-head {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    // gap: 2px;
  }

  .content {
    display: flex;
    flex-direction: column;

    .robot-type {
      display: flex;
      margin-top: 5px;
      > span {
        font-family: DIN Alternate;
        font-size: 10px;
        text-align: center;
        // font-weight: 600;
        color: #fff5ee;
        padding: 1px 2px;
        display: inline-block;
        background-color: #878787;
        border-radius: 2px;
      }
    }

    .value-wrapper {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 2px;

      > .value {
        flex: 1;
        font-family: DIN Alternate;
        font-size: 14px;
        text-align: center;
        font-weight: 900;
        color: #0084f4;
        background: rgba(255, 255, 255, 0.6);
      }
    }
  }
}
</style>
