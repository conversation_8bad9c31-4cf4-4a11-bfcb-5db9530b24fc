import layerConfig from "./layerConfig";
import History from "../history/History";
import EventBus from "../eventBus/EventBus";
// import Selected from '../selected/Selected'
const Manager = new Map();

class LayerManager {
  //初始化图层
  static initLayer() {
    layerConfig.forEach(config => {
      const { id, layerClass, zIndex } = config;
      const layerInstance = new layerClass();
      const initOp = { id, zIndex };
      layerInstance.initLayer(initOp);
      Manager.set(id, layerInstance);
    });
  }
  //初始化
  static initElements(op) {
    const { id, data } = op;
    const instance = Manager.get(id);
    if (!instance) return;
    instance.initElements(data);
  }
  //添加
  static addElements(op) {
    const { id, data, isSaveHistory = true, historyContinue, isEmitData = true } = op;
    const instance = Manager.get(id);
    if (!instance) return;
    const { historyDetail, emitData } = instance.addElements(data);
    if (historyContinue !== undefined) {
      historyDetail.historyContinue = historyContinue;
    }
    if (isSaveHistory && historyDetail) History.add(historyDetail);
    if (emitData && isEmitData) EventBus.$emit("added", emitData);
  }
  //更新
  static updateElements(op) {
    const { id, data, isSaveHistory = true, isCoverProperties = false } = op;
    const instance = Manager.get(id);
    //当更新时，增加字段，用来标记，该元素发生变化,用于后端识别进行增量更新
    const changeData = data.map(item => {
      item.isChanged = true;
      return item;
    });
    const { historyDetail, emitData } = instance.updateElements(changeData, isCoverProperties);
    if (isSaveHistory && historyDetail) History.add(historyDetail);
    if (emitData) EventBus.$emit("updated", emitData);
  }
  //删除
  static deleteElements(op) {
    const { id, data, isSaveHistory = true, historyContinue, isEmitData = true } = op;
    const instance = Manager.get(id);
    const { historyDetail, emitData } = instance.deleteElements(data);
    if (historyContinue !== undefined) {
      historyDetail.historyContinue = historyContinue;
    }
    if (isSaveHistory && historyDetail) History.add(historyDetail);
    if (emitData && isEmitData) EventBus.$emit("deleted", emitData);
    // Selected.resetAllSelected()
  }
  //显示图层
  static showLayer(ops) {
    //cellType针对cell图层
    const { id, type: cellType } = ops;
    const instance = Manager.get(id);
    instance.showLayer(cellType);
  }
  //隐藏如曾
  static hideLayer(ops) {
    //cellType针对cell图层
    const { id, type: cellType } = ops;
    const instance = Manager.get(id);
    instance.hideLayer(cellType);
  }
  static get(layerName) {
    return Manager.get(layerName);
  }
  // static getProperties1(layerName,id) {
  //   const layerInstance = this.get(layerName)
  //   return layerInstance.getProperties(id)
  // }
  // static getProperties(elementId) {
  //   const ManagerArr = [...Manager];
  //   for(let i = 0,len = ManagerArr.length ;i < len;i++){
  //     const [layerId,layerInstance] = ManagerArr[i]
  //     //operateLayer图层没有该方法，特殊图层
  //     const properties = layerInstance.getProperties ? layerInstance.getProperties(elementId) : null
  //     if(properties){
  //       return {...properties}
  //     }
  //   }
  //   return null
  // }
  static getProperties({ layerName, id }) {
    if (!id) return console.error("要传id呀，铁子");
    const layerInstance = Manager.get(layerName);
    const properties = layerInstance.getProperties(id);
    return { ...properties };
  }
  static getAllData() {
    const obj = {};
    [...Manager].forEach(arr => {
      const [layerId, layerInstance] = arr;
      //operateLayer图层没有该方法，特殊图层
      if (layerInstance.getAllData) {
        obj[layerId] = layerInstance.getAllData();
      }
    });
    // //拼接被删除的数据
    // const deleteData = {}
    // for(const i in history.deleteData) {
    //   const ids = deleteData[i]
    //   deleteData[i.toUpperCase()] = ids
    // }
    // obj.deleteData = deleteData
    return obj;
  }
  static getLayerData(layerName) {
    const layerInstance = this.get(layerName);
    return layerInstance.getAllData();
  }
  //可点击层切换
  static triggerLayers(layerNames) {
    console.log("layerNames", layerNames);
    layerConfig.forEach(config => {
      const { id } = config;
      // if (["CELL", "AREA", "LINE", "DEVICE"].indexOf(id) !== -1) {
      const layerInstance = this.get(id);
      layerInstance.triggerLayer(false);
      // }
    });

    for (let i = 0, len = layerNames.length; i < len; i++) {
      switch (layerNames[i]) {
        case "CELL":
          const layerCell = this.get(layerNames[i]);
          layerCell.triggerLayer(true);
          break;
        case "AREA":
          const layerArea = this.get(layerNames[i]);
          layerArea.triggerLayer(true);

          break;
        case "LINE ":
          const layerLine = this.get(layerNames[i]);
          layerLine.triggerLayer(true);
          break;
        case "DEVICE":
          const layerDevice = this.get(layerNames[i]);
          layerDevice.triggerLayer(true);
          break;
        case "ALL":
          layerConfig.forEach(config => {
            const { id } = config;
            // if (["CELL", "AREA", "LINE", "DEVICE"].indexOf(id) !== -1) {
            const layerInstance = this.get(id);
            layerInstance.triggerLayer(true);
            // }
          });
      }
    }
  }

  static destroy() {
    Manager.clear();
  }
}
export default LayerManager;
