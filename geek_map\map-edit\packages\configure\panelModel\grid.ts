import { ToolPanelType } from "@packages/type/editUiType";
// [东, 南, 西, 北]
// 西
export const ADD_GRID_WEST: ToolPanelType = {
  option: {
    icon: "map-font-xi",
    name: "addGridWest",
    describe: "lang.rms.fed.west",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [1, 1, 0, 1],
  },
};

// 东
export const ADD_GRID_EAST: ToolPanelType = {
  option: {
    icon: "map-font-dong",
    name: "addGridEast",
    describe: "lang.rms.fed.east",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [0, 1, 1, 1],
  },
};

// 北
export const ADD_GRID_NORTH: ToolPanelType = {
  option: {
    icon: "map-font-bei",
    name: "addGridNorth",
    describe: "lang.rms.fed.north",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [1, 1, 1, 0],
  },
};

// 南
export const ADD_GRID_SOUTH: ToolPanelType = {
  option: {
    icon: "map-font-nan",
    name: "addGridSouth",
    describe: "lang.rms.fed.south",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [1, 0, 1, 1],
  },
};
// [东, 南, 西, 北]
// 西北
export const ADD_GRID_WEST_NORTH: ToolPanelType = {
  option: {
    icon: "map-font-xibei",
    name: "addGridWestNorth",
    describe: "lang.rms.fed.westAndNorth",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [1, 1, 0, 0],
  },
};

// 西南
export const ADD_GRID_WEST_SOUTH: ToolPanelType = {
  option: {
    icon: "map-font-xinan",
    name: "addGridWestSouth",
    describe: "lang.rms.fed.westAndSouth",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [1, 0, 0, 1],
  },
};

// 东北
export const ADD_GRID_EAST_NORTH: ToolPanelType = {
  option: {
    icon: "map-font-dongbei", 
    name: "addGridEastNorth",
    describe: "lang.rms.fed.eastAndNorth",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [0, 1, 1, 0],
  },
};
export const ADD_GRID_EAST_SOUTH: ToolPanelType = {
  option: {
    icon: "map-font-dongnan",
    name: "addGridEastSouth",
    describe: "lang.rms.fed.eastAndSouth",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [0, 0, 1, 1],
  },
};
// 东西
export const ADD_GRID_EAST_WEST: ToolPanelType = {
  option: {
    icon: "map-font-dongxi",
    name: "addGridEastWest",
    describe: "lang.rms.fed.eastAndWest",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [0, 1, 0, 1],
  },
};
// 南北
export const ADD_GRID_SOUTH_NORTH: ToolPanelType = {
  option: { 
    icon: "map-font-nanbei",
    name: "addGridSouthNorth",
    describe: "lang.rms.fed.northAndSouth", 
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [1, 0, 1, 0],
  },
};

// [东, 南, 西, 北]
// 东西北
export const ADD_GRID_EAST_WEST_NORTH: ToolPanelType = {
  option: {
    icon: "map-font-dongxibei",
    name: "addGridEastWestNorth",
    describe: "lang.rms.fed.eastAndWestAndSouth",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [0, 1, 0, 0],
  },
};
// 东西南
export const ADD_GRID_EAST_WEST_SOUTH: ToolPanelType = {
  option: {
    icon: "map-font-dongxinan",
    name: "addGridEastWestSouth",
    describe: "lang.rms.fed.eastAndWestAndNorth",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [0, 0, 0, 1],
  },
};
// 西北南
export const ADD_GRID_WEST_NORTH_SOUTH: ToolPanelType = {
  option: {
    icon: "map-font-xibeinan",
    name: "addGridWestNorthSouth",
    describe: "lang.rms.fed.westAndNorthAndSouth",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [1, 0, 0, 0],
  },
};
// 东南北
export const ADD_GRID_EAST_SOUTH_NORTH: ToolPanelType = {
  option: {
    icon: "map-font-dongnanbei",
    name: "addGridEastSouthNorth",
    describe: "lang.rms.fed.eastAndSouthAndNorth",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [0, 0, 1, 0],
  },
};
// 东西南北
export const ADD_GRID_EAST_WEST_SOUTH_NORTH: ToolPanelType = {
  option: {
    icon: "map-font-dongnanxibei",
    name: "addGridEastWastSouthNorth",
    describe: "lang.rms.fed.westAndEastAndSouthAndNorth",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [0, 0, 0, 0],
  },
};
// 无方向
export const ADD_GRID_NONE: ToolPanelType = {
  option: {
    icon: "map-font-guanbi",
    name: "addGridNone",
    describe: "lang.rms.fed.noDirection",
    group: "grid",
    isSelect: true,
    eventName: "map:addGrid",
    grid: [1, 1, 1, 1],
  },
};
