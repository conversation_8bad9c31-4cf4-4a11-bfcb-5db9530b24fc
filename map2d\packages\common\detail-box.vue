<template>
  <div class="map-right-detail-box">
    <h6 @click="handleToggleCollapse">
      <el-icon v-if="expand" class="icon" :class="{ active: isCollapse }">
        <gp-icon-caret-right />
      </el-icon>
      {{ $t(title) }}
      <el-text
        v-show="showDetailBtn"
        size="small"
        type="primary"
        @click.stop="$emit('handleDetail')"
        class="detail-btn"
      >
        {{ $t("lang.rms.fed.textDetails") }}
      </el-text>
    </h6>

    <div class="content" :class="{ active: isCollapse }">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "Map2dRightDetailBox",
  props: {
    title: {
      type: String,
      default: "lang.rms.fed.detailedInformation",
    },
    expand: {
      type: Boolean,
      default: true,
    },
    showDetailBtn: {
      type: Boolean,
      default: false,
    },
    gridData: {
      type: Array,
      default: undefined,
    },
  },
  data() {
    return {
      isCollapse: true,
    };
  },
  methods: {
    handleToggleCollapse() {
      if (this.expand) {
        this.isCollapse = !this.isCollapse;
      } else {
        this.isCollapse = true;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.map-right-detail-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  border: 1px #c8cdd3 solid;
  overflow: hidden;

  > h6 {
    position: relative;
    .g-flex();
    justify-content: flex-start;
    font-size: 12px;
    line-height: 24px;
    gap: 6px;
    height: 24px;
    padding: 4px 4px 4px 2px;
    color: #4b5668;
    background-color: #e5e6eb;
    cursor: pointer;
    user-select: none;
    border-bottom: 1px #c8cdd3 solid;
    .icon {
      &.active {
        transform: rotate(90deg);
      }
    }
    .detail-btn {
      position: absolute;
      right: 10px;
      top: 0;

      &:hover {
        background: none !important;
      }
    }
  }

  > .content {
    height: 0;
    overflow: hidden;
    &.active {
      flex: 1;
      overflow: auto;
    }

    :deep(.map-right-grid) {
      &:nth-child(2n) {
        background-color: #f2f3f5;
      }
    }
  }
}
</style>
