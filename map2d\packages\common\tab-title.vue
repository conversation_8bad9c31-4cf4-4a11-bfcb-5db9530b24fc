<template>
  <h5 class="title">
    {{ $t(title) }}
    <template v-if="!!tips.length">
      <el-tooltip placement="bottom-start">
        <template #content>
          {{ Array.isArray(tips) ? tips.map(tip => $t(tip)).join("") : $t(tips) }}
        </template>
        <el-icon class="label-tips"><ElIconQuestionFilled /></el-icon>
      </el-tooltip>
    </template>
    <slot></slot>
  </h5>
</template>

<script>
export default {
  name: "Map2dRightTabTitle",
  props: {
    title: {
      type: String,
      default: "",
    },
    tips: {
      type: [String, Array],
      default: "",
    },
  },
};
</script>

<style lang="less" scoped>
h5.title {
  .g-flex();
  padding: 2px 0 6px;
  line-height: 16px;
  font-size: 13px;
  color: #4b5668;
  user-select: none;
}
.label-tips {
  font-size: 14px;
  height: 100%;
  color: #269bff;
  margin-left: 3px;
}
</style>
