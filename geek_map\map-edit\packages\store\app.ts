/**
 * app store
 * 这里的数据是由外界传入的
 */
import { defineStore } from "pinia";
import { store } from "@packages/logics/store";
import { ListenerMessage } from "@packages/type/listener";
import { setLanguageData, toName, setLanguage } from "@packages/logics/i18n";

export const CONF_KEYS = [
  "mapId",
  "floorId",
  "language",
  "sessionId",
  "autoI18n",
  "headerType",
  "mapTopToolConfig",
  "mapLeftToolConfig",
  "mapIconConfig",
];

/**
 * 获取默认值, 从url和localStorage中获取
 * 参数的设置优先级 url < localStorage < iframe.message
 **/
function handelLocalSearch(): ListenerMessage {
  const params: string[] = location.search.replaceAll("?", "").split("&");
  let data: { [key: string]: any } = {};
  params.forEach((item: string) => {
    if (item) {
      const [key, val] = item.split("=");
      if (!val) {
        return;
      }
      switch (val.toLowerCase()) {
        case "true":
          data[key] = true;
          break;
        case "false":
          data[key] = false;
          break;
        default:
          data[key] = val;
          break;
      }
    }
  });

  if (data?.localStorageId) {
    const localStorageData = JSON.parse(localStorage.getItem(data.localStorageId) || "{}");
    CONF_KEYS.forEach(key => {
      if (key in localStorageData) {
        data[key] = localStorageData[key];
      }
    });

    // languageData 比较特殊, 因为它不会存储在store中
    "languageData" in localStorageData &&
      setLanguageData(localStorageData.languageData, localStorageData.language);
  }

  data.mapId && (data.mapId = Number(data.mapId));
  data.floorId && (data.floorId = Number(data.floorId));
  setTimeout(() => {
    setLanguage(localStorage.curLanguage || "zhcn");
  }, 100)
  return data;
}

export const useAppStore = defineStore({
  id: "app",
  state: (): ListenerMessage => {
    return {
      mapId: undefined,
      floorId: undefined,
      language: localStorage.curLanguage,
      viewDevice: false,
      sessionId: "",
      autoI18n: true,
      ...handelLocalSearch(),
    };
  },
  getters: {
    doubleMapId(state): number | undefined {
      return state.mapId;
    },
    doubleFloorId(state): number | undefined {
      return state.floorId;
    },
  },
  actions: {
    setStoreData(key: string, value: any) {
      (this as any)[key] = value;
    },

    setLanguage(language: string) {
      const name = toName(language);
      console.log("设置语言 >", name);
      this.language = name;
      setLanguage(language);
    },
  },
});

export function use() {
  return useAppStore(store);
}
