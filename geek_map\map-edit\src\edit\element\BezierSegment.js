import BezierLine from './baseElement/BezierLine'
import {Graphics} from "pixi.js";
// import { SmoothGraphics as Graphics } from '@pixi/graphics-smooth';
import LineHitArea from "./baseElement/LineHitArea";
import CtrlPoint from './baseElement/CtrlPoint'
import DashLine from "./baseElement/DashLine";
import <PERSON>zierArrow from "./baseElement/BezierArrow"
import Selected from "../selected/Selected";
import Control from "../control/Control";
import {isHoverNode, pixi2cad} from "../utils/utils";
import {cad2pixi} from '../utils/utils'
import Store from "../store/Store";
import {lineStyle} from "../config";
import LayerManager from "../layerManager/LayerManager";
const {INACTIVE_LINE,ACTIVE_LINE} = lineStyle
export default class BezierSegment {
  //保存点下那一瞬间的点数据
  static _downInfo = null
  //创建
  static add(data) {
    const {points,unloadDirs,loadDirs,segmentType,segmentId} = data
    //y轴倒置，CAD和canvas的坐标系不同
    const invertPoints = points.map(item => {
      return cad2pixi(item)
    })
    if(!invertPoints || invertPoints.length < 2) return null
    const $container = new Graphics();
    $container.buttonMode = true
    $container.interactive = true
    $container.interactiveChildren = true
    $container.sortableChildren = true
    $container.segmentId = segmentId
    //当前线
    const $line = new Graphics()
    $line.name = 'line'
    $line.interactive = true
    $line.cursor = 'pointer'
    $line.loadDirs = loadDirs
    $line.unloadDirs = unloadDirs
    $line.zIndex = 1
    //虚线
    const $dashLine = new Graphics()
    $dashLine.name = 'dashLine'
    $dashLine.zIndex = 2
    $dashLine.visible = false
    DashLine.render($dashLine,invertPoints)
    //渲染点
    //渲染点，将点加入缓存
    // const cacheName = 'lineCtrlPoint'
    invertPoints.forEach((item,index) => {
      // let $point
      const {x,y,nodeId,cellCode} = item
      // const pointCache = Cache.get(cacheName)
      // if(!pointCache){
      //   $point = CtrlPoint.render(x,y)
      //   Cache.set(cacheName,$point)
      // }else{
      //   $point = pointCache.clone()
      //   $point.position.set(x,y)
      // }
      const $point = CtrlPoint.render(x,y)
      $point.visible = false
      $point.name = 'point'
      $point.nodeId = nodeId
      $point.cellCode = cellCode
      //判断是否为控制点
      $point.isCtrl = [1,2].includes(index)
      //将节点与线的关系进行存储
      if(nodeId) Store.node2Line.insert(nodeId,segmentId)
      $point.zIndex = 3
      $container.addChild($point)

      $point
        .on('pointerdown',(e) => {
          if(!$point.isCtrl){
            const {x,y,nodeId,cellCode} = $point
            this._downInfo = {x,y,nodeId,cellCode}
          }
          Control.enableDrag(false)
          $point.alpha = 0.5;
          $point.dragging = true;
        })
        .on('pointerup', (e) => {
          Control.enableDrag(true)
          $point.alpha = 1;
          $point.dragging = false;
          if($point.isUpdate){
            this._updateFinished($point.parent)
            $point.isUpdate = false
          }
        })
        .on('pointerupoutside', () => {
          Control.enableDrag(true)
          $point.alpha = 1;
          $point.dragging = false;
          if($point.isUpdate){
            this._updateFinished($point.parent)
            $point.isUpdate = false
          }
        })
        .on('pointermove',(e) => {
          if ($point.dragging) {
            const p = e.data.getLocalPosition($point.parent);
            const hoverNode = isHoverNode(p)
            let updateAttr;
            if(hoverNode && !$point.isCtrl){
              const {x,y,nodeId,cellCode} = hoverNode
              updateAttr = {x,y,nodeId,cellCode}
              Store.node2Line.insert(nodeId,segmentId)
            }else{
              const origNodeId = $point.nodeId
              //非附着状态，移除点和线的关系
              if(origNodeId){
                Store.node2Line.delete(origNodeId,segmentId)
              }
              updateAttr = {x:p.x,y:p.y,nodeId:null,cellCode:null}
            }
            //设置为更新态
            if($point.x !== updateAttr.x || $point.y !== updateAttr.y) {
              $point.isUpdate = true
            }
            Object.assign($point,updateAttr)
            this._updateEditLine($point.parent)
          }
        })
    })
    BezierLine.render($line,invertPoints,{width:lineStyle.width,color: INACTIVE_LINE})
    //生成触发面
    const bounds = LineHitArea.render(lineStyle.width,$line.currentPath.points)
    $line.hitArea = bounds
    // this.renderBezierArrow($line)
    BezierArrow.render($line,{color: INACTIVE_LINE})
    // //点击触发
    // $line.on('pointertap',(e) => {
    //   const {segmentId} = $container
    //   Selected.renderSelected(segmentId,$container)
    // })
    $container.addChild($line)
    $container.addChild($dashLine)
    $container.type = 'BEZIER'
    return $container
  }
  //更新
  static update($el,item) {
    const {points,segmentId,unloadDirs,loadDirs} = item
    //y轴倒置，CAD和canvas的坐标系不同
    const invertPoints = points.map(item => {
      return cad2pixi(item)
    })
    const $line = $el.getChildByName('line')
    //元素属性赋值
    const elAttr = {unloadDirs,loadDirs}
    Object.assign($line,elAttr)
    const $dashLine = $el.getChildByName('dashLine')
    const $points = $el.children.filter(child => child.name === 'point')
    invertPoints.forEach((item,index) => {
      Object.assign($points[index],item)
    })
    $line.clear()
    $dashLine.clear()
    const $selected = Selected.getSelected(segmentId)
    const isActive = $selected && $selected.layerName === 'LINE'
    const color = isActive ? ACTIVE_LINE : INACTIVE_LINE
    BezierLine.render($line,invertPoints,{width:lineStyle.width,color})
    DashLine.render($dashLine,invertPoints)
    //设置可点击区域
    const bounds = LineHitArea.render(lineStyle.width,$line.currentPath.points)
    $line.hitArea = bounds
    BezierArrow.render($line,{color})
  }
  //更新线
  static _updateEditLine($el) {
    const $points = $el.children.filter(child => child.name === 'point')
    const $line = $el.getChildByName('line')
    const $dashLine = $el.getChildByName('dashLine')
    const paths = $points.map(p => {
      const {x,y,cellCode,nodeId} = p
      return {x,y,cellCode,nodeId}
    })
    $line.clear()
    $dashLine.clear()
    BezierLine.render($line,paths,{width:lineStyle.width,color: ACTIVE_LINE})
    DashLine.render($dashLine,paths)
    //设置可点击区域
    const bounds = LineHitArea.render(lineStyle.width,$line.currentPath.points)
    $line.hitArea = bounds
    BezierArrow.render($line,{color:ACTIVE_LINE})
    // this._addHitArea($hitArea,bounds)
  }
  //更新完成触发
  static _updateFinished($el) {
    const {segmentId,type:segmentType} = $el
    const $points = $el.children.filter(child => child.name === 'point')
    //筛选出非吸附点
    const $linkPoints = $points.filter($p => !$p.isCtrl)
    const isAllLink = $linkPoints.every($p => $p.nodeId)

    const points = $points.map(p => {
      const {x,y,cellCode,nodeId,isCtrl} = p
      if(nodeId){
        Store.node2Line.insert(nodeId,segmentId)
        return pixi2cad({x,y,cellCode,nodeId})
      }else if(!nodeId && !isCtrl) {
        Store.node2Line.insert(this._downInfo.nodeId,segmentId)
        return pixi2cad({...this._downInfo})
      }else{
        return pixi2cad({x,y,cellCode,nodeId})
      }
      // return pixi2cad({x,y,cellCode,nodeId})
    })
    const updateOp = {
      id:'LINE',
      data:[{segmentId,points,segmentType}],
      isSaveHistory:isAllLink
    }

    LayerManager.updateElements(updateOp)
    this._downInfo = null
    //更新回调触发
    // EventBus.$emit('updated',updateOp)
  }
}
