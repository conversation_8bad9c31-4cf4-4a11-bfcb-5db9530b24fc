<script>
import LatticeItem from "./lattice-item.vue";
import WeighIcon from "./icon/weigh-icon.vue";
import ScanIcon from "./icon/scan-icon.vue";
import { ElTooltip } from "element-plus";
import { h } from "vue";
export default {
  name: "map-right-park-item-virtual",
  props: ["errorKeys", "virtualRack", "currentSelected"],
  components: { LatticeItem },
  computed: {
    lattices() {
      const lattices = [];
      const rackLattices = this.virtualRack?.lattices || [];
      const directionByUpList = [];
      const directionByDownList = [];
      const directionByStationList = [];
      let downLayerMax = 0;
      let stationLayerMax = 0;
      rackLattices.forEach(latticeItem => {
        const { direction } = latticeItem;
        if (latticeItem.latticeType === "PP_STATION_TRANSFER") {
          if (direction === "DOWN") {
            directionByDownList.push(latticeItem);
          } else if (direction === "UP") {
            directionByUpList.push(latticeItem);
          }
        } else {
          directionByStationList.push(latticeItem);
        }
      });

      if (directionByDownList.length) {
        directionByDownList.forEach(latticeItem => {
          const { rowIndex, colIndex } = latticeItem;
          if (!lattices[rowIndex]) {
            lattices[rowIndex] = [];
          }
          lattices[rowIndex][colIndex] = latticeItem;
          downLayerMax < rowIndex && (downLayerMax = rowIndex);
        });
      }

      if (directionByStationList.length) {
        stationLayerMax = downLayerMax;
        directionByStationList.forEach(latticeItem => {
          const { colIndex } = latticeItem;
          const rowIndex = latticeItem.rowIndex + downLayerMax;
          if (!lattices[rowIndex]) {
            lattices[rowIndex] = [];
          }
          lattices[rowIndex][colIndex] = latticeItem;
          stationLayerMax < rowIndex && (stationLayerMax = rowIndex);
        });
      }

      if (directionByUpList.length) {
        directionByUpList.forEach(latticeItem => {
          const { colIndex } = latticeItem;
          const rowIndex = latticeItem.rowIndex + stationLayerMax;
          if (!lattices[rowIndex]) {
            lattices[rowIndex] = [];
          }
          lattices[rowIndex][colIndex] = latticeItem;
        });
      }

      return lattices.reverse();
    },
  },
  render() {
    const lattices = this.lattices;
    const sumLayer = lattices?.length;
    if (!sumLayer) return "";

    const selected = this.currentSelected;
    const selectedType = selected?.type;
    return h(
      "div",
      { class: { "map2d-poppick-list": true, "map2d-station-list": true } },

      lattices.map((items, i) => {
        if (!items?.length) return null;
        return h(
          "div",
          { class: { "poppick-list-item": true } },
          items.map(item => {
            if (!item) return h(LatticeItem, { empty: true });
            return h(ElTooltip, { content: this.getTooltipContent(item), placement: "top" }, () => {
              return h(
                LatticeItem,
                {
                  class: { "station-lattice-item": true },
                  type: "virtual",
                  stationError: this.getParkError(item),
                  lattice: item,
                  isActiveLattice:
                    selectedType === "lattice" &&
                    selected.lattice?.latticeCode === item.latticeCode,
                  isActiveBox:
                    selectedType === "box" &&
                    selected.lattice?.relateBoxCode === item.relateBoxCode,
                  onSelect: () => {
                    this.$emit("click", item);
                  },
                },
                {
                  // NOTE 默认插槽和 2.x 不同，要写在第三个参数
                  icon: () => {
                    if (item.hasWeight && item.hasBcr) {
                      return [h(WeighIcon), h(ScanIcon)];
                    } else if (item.hasWeight) return h(WeighIcon);
                    else if (item.hasBcr) return h(ScanIcon);
                  },
                },
              );
            });
          }),
        );
      }),
    );
  },

  methods: {
    getParkError(lattice) {
      const errorKeys = this.errorKeys;
      const latticeCode = lattice?.latticeCode || "";
      const relateBoxCode = lattice?.relateBoxCode || "";

      return !!errorKeys[latticeCode] || !!errorKeys[relateBoxCode];
    },

    getTooltipContent(lattice) {
      const errorKeys = this.errorKeys;
      const parkError = this.getParkError(lattice);

      const relateBoxCode = lattice?.relateBoxCode;
      const latticeCode = lattice?.latticeCode;

      let status = "";
      if (relateBoxCode) {
        // 货箱无任务
        if (parkError) {
          status = errorKeys[relateBoxCode];
          // 任务中
        } else if (lattice?.relateBox?.jobIds?.length || lattice?.relateBox?.goFetchJobs?.length) {
          status = "lang.rms.web.monitor.robot.working";
        }
      } else {
        if (lattice?.latticeStatus === "ALLOCATED" && lattice.latticeFlag === "NORMAL") {
          status = "lang.rms.fed.preOccupied";
        } else if (parkError) {
          status = errorKeys[latticeCode];
        }
      }

      if (status) return this.$t(status) + " - " + (relateBoxCode || latticeCode);
      else return relateBoxCode || latticeCode;
    },
  },
};
</script>

<style lang="less" scoped>
.map2d-station-list {
  border: 0 !important;
  .station-lattice-item {
    margin-bottom: 5px;
  }
}
</style>
