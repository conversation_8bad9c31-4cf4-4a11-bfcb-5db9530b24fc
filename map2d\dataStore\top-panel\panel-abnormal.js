import { defineStore } from "pinia";
import { getMap2D } from "@map2d/singleton";

/**
 * 这里是消息面板中异常信息的数据
 */
export const useMap2dAbnormalStore = defineStore("map2dAbnormalStore", {
  state: () => ({
    wsMessageCountInfo: {}, // websocket消息数量 代办
    wsEventMessageList: [], // websocket事件消息 处理为数组并排序后的数据
    _wsMessageInfoTimer: null, // websocket请求MessageInfo的定时器
    _wsCountInfoTimer: null, // websocket请求异常统计的定时器 （代办 机器人 设备 任务）

    deviceStat: {}, // 设备异常信息数据
    robotStat: {}, // 机器人异常信息数据
    robotStatDetail: [], // 机器人异常信息数据 - 按照不同型号存
    taskStat: {}, // 任务异常信息数据

    _deviceStatTimer: {}, // 获取设备异常信息数据定时器
    _robotStatTimer: {}, // 获取机器人异常信息数据定时器
    _taskStatTimer: {}, // 获取任务异常信息数据定时器

    _originMapAbnormalPanelVisible: true, // 是否显示左上角异常信息面板（原始值）
    mapAbnormalPanelVisible: true, // 是否显示左上角异常信息面板

    isAbnormalShow: false, // 是否显示左下下拉异常信息面板
    isPlayAudio: false, // 是否开启语音播报 播报的是MessageInfo的异常信息

    showRobotStatDetail: false, // 是否按机器人型号显示信息
  }),
  actions: {
    setMessageCountInfo(data) {
      this.wsMessageCountInfo = data;
      this._wsCountInfoTimer = setTimeout(() => {
        this.requestMessageTodoCount();
      }, 5000);
    },

    // 得到response的数据
    setEventMessageInfo(data) {
      let result = data?.result || [];
      result.sort((a, b) => {
        if (a.errorLevel < b.errorLevel) {
          return 1;
        }
        if (a.errorLevel > b.errorLevel) {
          return -1;
        }
        return 0;
      });

      this.wsEventMessageList = result;

      this._wsMessageInfoTimer = setTimeout(() => {
        this._requestMessageInfo();
      }, 2000);
    },

    // 打开异常信息list
    setIsAbnormalShow(flag) {
      if (flag == this.isAbnormalShow) return;
      this.isAbnormalShow = flag;

      if (!this.isPlayAudio) {
        if (flag) this._requestMessageInfo();
        else {
          this.wsEventMessageList = [];
          this._clearTimer();
        }
      }
    },
    // 打开语音播报
    setIsPlayAudio(flag) {
      if (flag == this.isPlayAudio) return;
      this.isPlayAudio = flag;

      if (!this.isAbnormalShow) {
        if (flag) this._requestMessageInfo();
        else {
          this.wsEventMessageList = [];
          this._clearTimer();
        }
      }
    },

    // 设备异常 response
    setDeviceStat(data) {
      this.deviceStat = data || {};
      this._deviceStatTimer = setTimeout(() => {
        this._requestMessageDeviceStat();
      }, 1000);
    },

    // 机器人异常 response
    setRobotStat(data) {
      this.robotStat = data?.stat || {};
      this.robotStatDetail = data?.robotDetails || [];
      this._robotStatTimer = setTimeout(() => {
        this._requestMessageRobot();
      }, 1000);
    },

    // 任务异常 response
    setTaskStat(data) {
      this.taskStat = data || {};
      this._taskStatTimer = setTimeout(() => {
        this._requestMessageTaskStat();
      }, 1000);
    },

    // 设置机器人异常信息详情是否显示
    setMapAbnormalPanelVisible(flag, action = "origin") {
      switch (action) {
        case "rightDrawerAction":
          if (flag) {
            // 右侧抽屉关闭时，重新设置为原始值
            flag = this._originMapAbnormalPanelVisible;
          }
          break;
        default:
          this._originMapAbnormalPanelVisible = flag;
          break;
      }
      if (flag == this.mapAbnormalPanelVisible) return;
      this.mapAbnormalPanelVisible = flag;

      if (flag) {
        this.requestAllStat();
      } else {
        this._clearAllStatTimer();
      }
    },
    setShowRobotStatDetail(flag) {
      if (flag == this.showRobotStatDetail) return;
      this.showRobotStatDetail = flag;
    },

    // 代办数量统计 -- 初始化请求一次
    requestMessageTodoCount() {
      this._clearTimer(this._wsCountInfoTimer);
      const { mapWorker } = getMap2D() || {};
      mapWorker?.wsRequest("EventMessageCountRequestMsg");
    },

    // 面板重新打开 请求 机器人 设备 任务
    requestAllStat() {
      this._requestMessageDeviceStat();
      this._requestMessageRobot();
      this._requestMessageTaskStat();
    },

    _requestMessageInfo() {
      this._clearTimer(this._wsMessageInfoTimer);
      const { mapWorker } = getMap2D() || {};
      mapWorker?.wsRequest("EventMessageDeviceRequestMsg");
    },

    // 机器人状态统计 -- 初始化请求一次
    _requestMessageRobot() {
      this._clearTimer(this._robotStatTimer);
      const { mapWorker } = getMap2D() || {};
      mapWorker?.wsRequest("EventMessageRobotRequestMsg");
    },

    // 设备状态统计 -- 初始化请求一次
    _requestMessageDeviceStat() {
      this._clearTimer(this._deviceStatTimer);
      const { mapWorker } = getMap2D() || {};
      mapWorker?.wsRequest("DeviceStatRequestMsg");
    },

    // 任务状态统计 -- 初始化请求一次
    _requestMessageTaskStat() {
      this._clearTimer(this._taskStatTimer);
      const { mapWorker } = getMap2D() || {};
      mapWorker?.wsRequest("TaskStatRequestMsg");
    },

    // 面板关闭 清除定时器 机器人 设备 任务
    _clearAllStatTimer() {
      this._clearTimer(this._deviceStatTimer);
      this._clearTimer(this._robotStatTimer);
      this._clearTimer(this._taskStatTimer);
    },

    _clearTimer(customTimer) {
      if (customTimer) {
        clearTimeout(customTimer);
        customTimer = null;
      }
    },
  },
});
