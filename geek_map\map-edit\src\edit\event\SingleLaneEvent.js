import Mode from '../Mode'
import Selected from "../selected/Selected";
import Event from "./Event";
import EventBus from "../eventBus/EventBus";
export default class SingleLaneEvent {
  static addEvents() {
    // const {options:{defaultOps = {}}} = Mode.mode
    EventBus.$emit('message', { type: 'info', text: 'lang.rms.fed.chooseCell' })
    //模式为多选
    Selected.isMultipleSelected = true
    let timestamp = null
    //选择
    const selected = (e) => {
      const target = e.event.target
      if (target.name !== 'element') return
      const selectedId = target.nodeId
      if (Selected.isHasSelected(selectedId)) {
        Selected.resetSelected(selectedId)
      } else {
        Selected.renderSelected(selectedId, target)
      }
    }
    //重置
    const finished = () => {
      timestamp = null
      Selected.resetAllSelected()
      Mode.resetMode()
    }
    const events = {
      clicked: e => {
        if (Event.isRightClick) return Event.isRightClick = false
        if (Date.now() - timestamp > 300) return timestamp = null
        selected(e)
      },
      keydown: e => {
        const { key } = e
        if (key === 'Escape') {
          finished()
          EventBus.$emit('keydown:Escape')
        }
      },
      mousedown: e => {
        timestamp = Date.now()
      },
    }
    return events
  }
}
