<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t(title)"
    :close-on-click-modal="false"
    fullscreen
    destroy-on-close
    modal-class="page-fullscreen-dialog"
    @close="close"
  >
    <div class="content">
      <model-box ref="modelBox" id="model_edit" :data="rowData" class="box-content" />

      <div class="form-content">
        <rms-simple-form
          ref="categoryForm"
          :attrs="{ inline: false, 'label-width': 'auto' }"
          :configs="categoryFormConfigs"
          class="category-form"
        />

        <component
          v-if="componentFormName"
          ref="modelForm"
          :is="componentFormName"
          :isEdit="isEdit"
          :editData="rowData"
          @createModel="createModel"
          @modelSizeChange="modelSizeChange"
          class="model-form"
        />
      </div>
    </div>

    <!-- vsw 保存确认 弹层 -->
    <dialog-save-confirm ref="refSaveConfigDialog" :loading="loading" @save="save" />
    <template #footer>
      <span>
        <el-button @click="close">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button
          :loading="loading"
          type="primary"
          :disabled="!componentFormName"
          @click="beforeSave"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { required } from "@libs/rules";
import RmsSimpleForm from "@plugins/pageComponents/rms-simple-form.vue";
import BoxForm from "./editForm/box/box.vue";
import ShelfForm from "./editForm/shelf/shelf.vue";
import PalletForm from "./editForm/pallet/pallet.vue";
import HolderForm from "./editForm/holder/holder.vue";
import PalletRackForm from "./editForm/palletRack/palletRack.vue";
import PpShelfForm from "./editForm/ppShelf/ppShelf.vue";
import VswForm from "./editForm/vsw/vsw.vue";
import ModelBox from "./modelBox/index.vue";
import DialogSaveConfirm from "./dialogSaveConfirm.vue";

export default {
  name: "ContainerModelManageEditDialog",
  components: {
    RmsSimpleForm,
    BoxForm,
    ModelBox,
    ShelfForm,
    PalletForm,
    HolderForm,
    PalletRackForm,
    PpShelfForm,
    VswForm,
    DialogSaveConfirm,
  },
  props: ["categoryDict"],
  data() {
    return {
      dialogVisible: false,
      loading: false,

      rowData: null,
      saveParams: null,
      isEdit: true,
      componentFormName: "",
      componentNameDict: {
        BOX: "BoxForm",
        SHELF: "ShelfForm",
        PALLET: "PalletForm",
        X_PALLET: "PalletForm",
        SHELF_HOLDER: "HolderForm",
        X_HOLDER: "HolderForm",
        PALLET_RACK: "PalletRackForm",
        PPP_SHELF: "PpShelfForm",
        VSW_BIN: "VswForm",
      },
      categoryFormConfigs: [], // 分类表单配置
    };
  },
  computed: {
    title() {
      return this.rowData?.id
        ? this.$t("lang.rms.web.container.editContainerModel")
        : this.$t("lang.rms.web.container.addContainerType");
    },
  },
  methods: {
    open(row) {
      if (row) {
        this.isEdit = true;
        this.rowData = JSON.parse(JSON.stringify(row));
      } else {
        this.isEdit = false;
        this.rowData = null;
        this.saveParams = null;
      }
      this.categoryFormConfigs = this._getCategoryFormConfigs(this.rowData, this.isEdit);
      this.dialogVisible = true;
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.dialogVisible = false;
    },

    beforeSave() {
      const formData = this.rowData;
      this.$refs["modelForm"].validate().then(modelData => {
        const params = Object.assign({}, formData, modelData);

        const modelCategoryList = this.categoryFormConfigs[0].options;
        const category = formData?.extendJson?.subCategory;
        const modelCategory =
          modelCategoryList.find(item => item.value === category)?.category || category;
        params.modelCategory = modelCategory;
        params.saveMode = 0; //
        this.saveParams = params;

        // vsw 保存确认 立即生效 重启生效
        if (category === "VSW_BIN") {
          this.$refs.refSaveConfigDialog.openDialog();
        } else {
          this.save();
        }
      });
    },

    save(saveMode = 0) {
      this.loading = true;
      const params = this.saveParams;
      params.saveMode = saveMode;
      console.log("params===========================", params);
      params.fetchDir = null;
      params.fetchDirsStr = null;
      params.fetchDirs = null;
      params.move = null;
      params.modelCategory = "";
      params.sizeTypes = null;
      params.subType = "PALLET";
      params.extendJson.subCategory = "PALLET_RACK";

      $req
        .post("/athena/containerModel/saveContainerModel", params)
        .then(res => {
          this.$emit("updateTableList");
          this.close();
          this.$success();
        })
        .finally(() => {
          this.loading = false;
        });
    },

    modelSizeChange(data) {
      Object.assign(this.rowData, data);
      this.$refs?.modelBox?.updateModel(this.rowData);
    },

    createModel(data = {}) {
      this.$refs?.modelBox?.createModel(Object.assign(this.rowData, data));
    },

    _getCategoryFormConfigs(row, isEdit = true) {
      const category = row?.extendJson?.subCategory || row?.modelCategory;
      this.componentFormName = this.componentNameDict[category];
      return [
        {
          prop: "category",
          label: "lang.rms.fed.containerCategory",
          tag: "select",
          defaultValue: category || "",
          disabled: isEdit,
          rules: [required()],
          options: this.categoryDict,
          attrs: {
            clearable: false,
            onChange: val => {
              this.rowData = { extendJson: { subCategory: val } };
              this.componentFormName = this.componentNameDict[val];
            },
          },
        },
      ];
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  padding: 12px 0;
  display: flex;
  flex-direction: row;
  height: 100%;
  overflow: auto;
  .box-content {
    height: 100%;
    flex: 0 0 320px;
  }
  .form-content {
    height: 100%;
    flex: 1;
    padding: 0 12px 0 12px;
    overflow: auto;

    .category-form {
      border-bottom: 1px solid #ebeef5;
      margin-bottom: 12px;
      :deep(.rms-select) {
        width: 200px;
      }
    }

    .model-form {
      :deep(.rms-input) {
        width: 200px;
      }
      :deep(.rms-select) {
        width: 200px;
      }
      :deep(.rms-input-number) {
        width: 200px;
      }
    }
  }
}
</style>
