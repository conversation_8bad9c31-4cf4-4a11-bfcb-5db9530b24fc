<template>
  <h6 class="lattice-header">
    <span v-show="code" class="title">
      {{ code }}
    </span>
    <el-radio-group
      v-if="showSide"
      size="small"
      v-model="side"
      class="poppick-radio"
      @input="radioChange"
    >
      <el-radio-button v-for="item in sides" :key="item" :label="item" :value="item" />
    </el-radio-group>
  </h6>
</template>

<script>
export default {
  name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  props: {
    showSide: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "lang.rms.fed.container",
    },
    code: {
      type: [String, Number],
      default: "--",
    },
    defaultSide: {
      type: String,
      default: "F",
    },
    sides: {
      type: Array,
      default: () => [],
    },
    sideChange: {
      type: Function,
      // default: () => [],
    },
  },
  data() {
    return {
      value: true,
      side: this.defaultSide || "F",
    };
  },
  watch: {
    defaultSide: {
      handler(val) {
        this.side = val;
      },
      immediate: true,
    },
  },
  methods: {
    showBox() {
      const flag = !this.value;
      this.value = flag;
    },
    radioChange(e) {
      const value = e.target.value;
      this.side = value;
      this.sideChange(value);
    },
  },
};
</script>

<style lang="less" scoped>
.lattice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #a69548;
  border-bottom: 0;
  padding: 3px 0 3px 2px;
  .title {
    font-size: 13px;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: 600;
    color: #4b5668;
    background: #fff;
    border-radius: 3px;
  }
  strong {
    font-weight: 600;
    padding-left: 5px;
  }
}

.poppick-radio {
  :deep(.rms-radio-button__inner) {
    height: 20px;
    border: 0 !important;
    transition: none;
  }
}
</style>
