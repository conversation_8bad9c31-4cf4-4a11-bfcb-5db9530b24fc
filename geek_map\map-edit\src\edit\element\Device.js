import Resource from "../resource/Resource";
import { Sprite } from "pixi.js";
import { cad2pixi } from "../utils/utils";
import { defaultConfig, COEFFICIENT } from "../config";
const { DEVICE } = defaultConfig;
export default class Device {
  //设备的默认属性
  static name = "device";
  static w = DEVICE.width;
  static l = DEVICE.length;
  static add(data) {
    const { location, cellCode, id, mapEditItemId } = data;
    //需要绑定在元素上的属性
    const elAttr = { cellCode, id, mapEditItemId };
    if (!location) return null;
    const resources = Resource.loader.resources;
    const texture = resources[this.name].texture;
    const pixiLocation = cad2pixi(location);
    const $el = new Sprite(texture);
    const { x, y } = pixiLocation;
    $el.buttonMode = true;
    $el.interactive = true;
    $el.x = x;
    $el.y = y;
    $el.width = this.l / COEFFICIENT;
    $el.height = this.w / COEFFICIENT;
    $el.anchor.set(0.5, 0.5);
    //说是设备图表太大了，缩小下
    $el.scale.set(0.2, 0.2);
    $el.zIndex = 1;
    $el.type = "device";
    // $el.name = this.name
    Object.assign($el, elAttr);
    //绑定事件
    this.bindEvent($el);
    return $el;
  }
  static update($el, data) {
    const { location, cellCode, id } = data;
    const pixiLocation = cad2pixi(location);
    const elAttr = {
      x: pixiLocation.x,
      y: pixiLocation.y,
      cellCode,
      id,
    };
    Object.assign($el, elAttr);
  }
}
