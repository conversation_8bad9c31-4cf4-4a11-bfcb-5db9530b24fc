import { defineStore } from "pinia";

export const useMap2dInfoStore = defineStore("map2dInfoStore", {
  state: () => ({
    mapColor: {
      BG: 0xeeeeee,
    },
    // 地图初始化配置信息
    mapSetting: {
      viewBorder: false,
      mapAngle: "--",
      mapArrangeDir: 1,
      classicIcon: false,
    },

    _MapUpdateResponseMap: {},
    fps: 0, // 地图帧率

    mapInfo: null, // 地图楼层数据
  }),
  getters: {
    mapArrangeDir(state) {
      // 地图排列方向
      return state.mapSetting.mapArrangeDir;
    },
    mapAngle(state) {
      // 地图角度
      return state.mapSetting.mapAngle;
    },
    mapReleased(state) {
      const mapInfo = state.mapInfo || {};
      if (mapInfo.hasOwnProperty("released")) {
        return mapInfo.released;
      } else {
        return true;
      }
    },
  },
  actions: {
    setMapColor(colors) {
      this.mapColor = Object.assign(this.mapColor, colors);
    },
    setMapSetting(setting) {
      this.mapSetting = Object.assign(this.mapSetting, setting);
    },

    setMapInfo(data) {
      this.mapInfo = data;
    },

    setMapReleased(released) {
      let mapInfo = this.mapInfo;
      if (!mapInfo) mapInfo = {};
      this.mapInfo = Object.assign({}, mapInfo, { released });
    },

    setMapAngle(angle) {
      if (angle === this.mapSetting.mapAngle) return;
      this.setMapSetting({ mapAngle: angle });
    },

    calFps(flag) {
      if (flag) this._MapUpdateResponseMap = {};
      let t = String(Date.now()).slice(0, 10);
      const MapUpdateResponseMap = this._MapUpdateResponseMap;

      if (MapUpdateResponseMap[t]) {
        MapUpdateResponseMap[t] = MapUpdateResponseMap[t] + 1;
      } else {
        let map_length = 0;
        let response_count = 0;
        for (let key in MapUpdateResponseMap) {
          if (MapUpdateResponseMap[key] < 3) continue;
          map_length++;
          response_count += MapUpdateResponseMap[key];
        }

        let fps = response_count / map_length;
        if (isNaN(fps)) fps = "";
        else fps = fps.toFixed(1);
        this.fps = fps;

        MapUpdateResponseMap[t] = 1;
      }
      let keys = Object.keys(MapUpdateResponseMap);
      if (keys.length > 60) {
        delete MapUpdateResponseMap[keys[0]];
      }
    },

    resetMap2dInfoStore() {
      this.$reset();
    },
  },
});
