/**
 * 这里声明了外部能够传入的数据 ListenerMessage
 * 以及外部在自定义时使用的模板数据 MapDataConf
 */

import { ToolPanelType, IconConfType } from "@packages/type/editUiType";

export interface ListenerMessage {
  mapId?: number;
  floorId?: number;
  viewDevice?: boolean;
  language?: string;
  sessionId?: string;
  autoI18n?: boolean;
  headerType?: string;
  languageData?: {
    [key: string]: any;
  };

  // 左侧和上方的菜单
  mapTopToolConfig?: ToolPanelType;
  mapLeftToolConfig?: ToolPanelType;
  // 地图右上方的快捷按钮
  mapIconConfig?: IconConfType[];
}

export interface MapDataConf {
  resolution?: number;
  locationX?: number;
  locationY?: number;
  scale?: number;
  mapName?: string;
  mapId?: string | number;
  floorId?: string | number;
  splitImage?: any;
  status?: any
}
