<template>
  <div class="map-abnormal-panel">
    <abnormal-number
      :ref="`refAbnormalNumberRobot`"
      title="lang.rms.fed.robot"
      :total-count="robotStat?.totalCount"
      :exception-count="robotStat?.exceptionCount"
      :options="robotOptions"
      :showDetailSwitch="isShowDetailSwitch"
      :showRobotStatDetail="showRobotStatDetail"
      @showDetail="val => setShowRobotStatDetail(val)"
      @titleClick="goRobotMonitor({})"
      @statusClick="goRobotMonitor"
    />

    <!-- task -->
    <abnormal-number
      title="lang.rms.config.group.job"
      :total-count="taskStat?.totalJobCount"
      isHideExceptionCount
      :options="taskOptions"
      @titleClick="goTaskManageByTaskType({})"
      @statusClick="goTaskManageByTaskType"
    />

    <abnormal-number
      v-show="deviceStat.totalSize"
      title="lang.rms.config.group.dmp"
      :total-count="deviceStat?.totalSize"
      :exception-count="deviceStat?.exceptionSize"
      :options="deviceOptions"
      @titleClick="goTaskManageByDeviceTask({})"
      @statusClick="goTaskManageByDeviceTask"
    />

    <div
      class="drop-down"
      :class="{ 'is-active': isAbnormalShow }"
      @click="setIsAbnormalShow(!isAbnormalShow)"
    >
      <span class="text">{{ $t("lang.rms.web.monitor.exception.info") }}</span>
      <el-icon class="drop-icon" :size="16"><gp-icon-arrow-down /></el-icon>
    </div>

    <abnormal-list v-if="isAbnormalShow" @showInfo="selectInfoData" />

    <!-- 按机器人型号显示信息 -->
    <abnormal-number-detail
      v-show="isShowAbnormalDetail"
      class="robot-type-detail"
      :options="robotOptions"
      :detail="robotStatDetail"
    />
  </div>
</template>

<script>
import { mapState, mapActions } from "pinia";
import { useMap2dAbnormalStore } from "@map2d/dataStore/top-panel/panel-abnormal";

import AbnormalNumber from "./components/abnormal-number.vue";
import AbnormalList from "./components/abnormal-list.vue";
import AbnormalNumberDetail from "./components/abnormal-number-detail.vue";

export default {
  name: "Map2dAbnormal",
  components: { AbnormalNumber, AbnormalList, AbnormalNumberDetail },
  data() {
    return {
      abnormalDialogVisible: false,
      infoDialogData: {},
    };
  },
  computed: {
    ...mapState(useMap2dAbnormalStore, [
      "robotStat",
      "taskStat",
      "deviceStat",
      "isAbnormalShow",
      "robotStatDetail",
      "showRobotStatDetail",
    ]),

    robotOptions() {
      const robotStat = this.robotStat || {};
      return [
        {
          label: "lang.rms.fed.work",
          icon: "icon-gongzuo",
          status: "work",
          statusInSystem: "WORKING",
          value: "workingCount" in robotStat ? robotStat.workingCount : "-",
        },
        {
          label: "lang.rms.fed.chargerIdle",
          icon: "icon-kongxian",
          status: "idle",
          statusInSystem: "IDLE",
          value: "idleCount" in robotStat ? robotStat.idleCount : "-",
        },
        {
          label: "lang.rms.config.group.charging",
          icon: "icon-chongdian",
          status: "charging",
          statusInSystem: "CHARGING",
          value: "chargingCount" in robotStat ? robotStat.chargingCount : "-",
        },
        {
          label: "lang.rms.fed.unusual",
          icon: "icon-abnormal",
          status: "abnormal",
          statusInSystem: "ERROR",
          value: "sleepingCount" in robotStat ? robotStat.exceptionCount : "-",
        },
        {
          label: "lang.rms.fed.offline",
          icon: "icon-lixian",
          status: "offline",
          statusInSystem: "DISCONNECTED",
          value: "disconnectedCount" in robotStat ? robotStat.disconnectedCount : "-",
        },
        {
          label: "lang.rms.fed.sleep",
          icon: "icon-xiumian2",
          status: "sleep",
          statusInSystem: "SLEEPING",
          value: "sleepingCount" in robotStat ? robotStat.sleepingCount : "-",
        },
        {
          label: "lang.rms.fed.remove",
          icon: "icon-yichu1",
          status: "remove",
          statusInSystem: "REMOVED",
          value: "removedCount" in robotStat ? robotStat.removedCount : "-",
        },
      ];
    },

    taskOptions() {
      const taskStat = this.taskStat || {};
      return [
        {
          label: "lang.rms.box.boxstate.new",
          icon: "icon-daifenpei1",
          jobState: "NEW",
          value: "newJobCount" in taskStat ? taskStat.newJobCount : "-",
        },
        {
          label: "lang.rms.fed.allocated",
          icon: "icon-yifenpei1",
          jobState: "ASSIGNED",
          value: "assignJobCount" in taskStat ? taskStat.assignJobCount : "-",
        },
        {
          label: "lang.rms.robot.exestatus.executing",
          icon: "icon-zhihangzhong",
          jobState: "EXECUTING",
          value: "executingJobCount" in taskStat ? taskStat.executingJobCount : "-",
        },
      ];
    },

    deviceOptions() {
      const deviceStat = this.deviceStat || {};
      return [
        {
          label: "lang.rms.fed.work",
          icon: "icon-zhihangzhong",
          value: "workingSize" in deviceStat ? deviceStat.workingSize : "-",
        },
        {
          label: "lang.rms.fed.chargerIdle",
          icon: "icon-kongxian",
          value: "idleSize" in deviceStat ? deviceStat.idleSize : "-",
        },
        {
          label: "lang.rms.fed.offline",
          icon: "icon-lixian",
          value: "offlineSize" in deviceStat ? deviceStat.offlineSize : "-",
        },
      ];
    },

    isShowDetailSwitch() {
      // 这里 robotStatDetail的数据如果只有一条或者没有, 就不展示这个开关了
      const len = this.robotStatDetail?.length || 0;
      return len > 1;
    },

    isShowAbnormalDetail() {
      // 是否展示右侧按型号显示的详情
      // 1. swtich开关处于打开状态
      // 2. switch是可见的(isShowDetailSwitch)
      const { showRobotStatDetail, isShowDetailSwitch } = this;
      return showRobotStatDetail && isShowDetailSwitch;
    },
  },

  mounted() {
    if (this.showRobotStatDetail) {
      this.$refs.refAbnormalNumberRobot.setDetailSwitch(this.showRobotStatDetail);
    }
  },
  unmounted() {
    this.setIsAbnormalShow(false);
  },
  methods: {
    ...mapActions(useMap2dAbnormalStore, ["setIsAbnormalShow", "setShowRobotStatDetail"]),

    selectInfoData(data) {
      this.infoDialogData = data;
      this.abnormalDialogVisible = true;
    },

    goRobotMonitor({ statusInSystem }) {
      let route;
      if (statusInSystem) {
        route = this.$router.resolve({
          path: "/warehouseManage/robotMonitor",
          query: { statusInSystem },
        });
      } else {
        route = this.$router.resolve({
          path: "/warehouseManage/robotMonitor",
        });
      }
      // 在新标签页中打开目标路由
      window.open(route.href, "_blank");
    },

    goTaskManageByTaskType({ jobState }) {
      const route = this.$router.resolve({
        path: "/warehouseManage/taskManage",
        query: { type: "TaskType", jobState },
      });
      // 在新标签页中打开目标路由
      window.open(route.href, "_blank");
    },

    goTaskManageByDeviceTask() {
      // todo: 这里监控中的状态和任务管理中的状态不一致, 产品说暂时仅跳转
      const route = this.$router.resolve({
        path: "/warehouseManage/taskManage",
        query: { type: "DeviceTask" },
      });
      // 在新标签页中打开目标路由
      window.open(route.href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.map-abnormal-panel {
  position: absolute;
  display: flex;
  flex-direction: column;
  top: @map2d-top-toolbar-height;
  left: 0px;
  width: 256px;
  padding: 0 5px 6px;
  min-height: 50px;
  max-height: 80%;
  z-index: 11;

  border-radius: 2px;
  backdrop-filter: blur(4.5px);
  box-shadow:
    0 4px 8px 0 rgba(0, 0, 0, 0.2),
    0 6px 20px 0 rgba(0, 0, 0, 0.19);
  background-color: rgba(247, 248, 250, 0.9);
  .drop-down {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 20px;
    line-height: 20px;
    margin-top: 6px;
    font-size: 12px;
    cursor: pointer;
    background-color: #d5deee;
    > .text {
      padding-right: 3px;
    }
    > .drop-icon {
      color: #4b5668;
      transition: all 0.3s;
      transform: rotateZ(0px);
    }

    &:hover {
      opacity: 0.8;
    }

    &.is-active {
      font-weight: 800;
      > .text {
        font-weight: 800;
        opacity: 0.9;
      }
      > .drop-icon {
        transform: rotateZ(180deg);
      }
    }
  }

  .robot-type-detail {
    position: absolute;
    width: 256px;
    min-height: 50px;
    left: 100%;
    top: 0;
    border-radius: 2px;
    backdrop-filter: blur(4.5px);
    box-shadow:
      0 4px 8px 0 rgba(0, 0, 0, 0.2),
      0 6px 20px 0 rgba(0, 0, 0, 0.19);
    background-color: rgba(247, 248, 250, 0.9);
  }
}
</style>
