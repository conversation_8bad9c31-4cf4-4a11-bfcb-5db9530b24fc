// @ts-check
const { defineConfig } = require("eslint-define-config");

module.exports = defineConfig({
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  parser: "vue-eslint-parser",
  parserOptions: {
    parser: "@typescript-eslint/parser",
    ecmaVersion: 2020,
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  extends: [
    "plugin:vue/vue3-recommended",
    "plugin:@typescript-eslint/recommended",
    "prettier",
    "plugin:prettier/recommended",
  ],
  rules: {
    // typescript 规则
    "@typescript-eslint/ban-ts-ignore": "off",
    // 需要函数和类方法的显式返回类型
    "@typescript-eslint/explicit-function-return-type": "off",
    // 禁止使用any
    "@typescript-eslint/no-explicit-any": "off",
    // 除 import 语句外，禁止require语句
    "@typescript-eslint/no-var-requires": "off",
    // 禁止空函数
    "@typescript-eslint/no-empty-function": "off",
    // 在定义之前禁止使用变量
    "@typescript-eslint/no-use-before-define": ["error"],
    // 禁止@ts-<directive>评论或要求指令后的描述
    "@typescript-eslint/ban-ts-comment": "off",
    // 禁止类型
    "@typescript-eslint/ban-types": "off",
    // 禁止使用后缀运算符的非空断言
    "@typescript-eslint/no-non-null-assertion": "off",
    // 要求导出函数和类的公共类方法的显式返回和参数类型。
    "@typescript-eslint/explicit-module-boundary-types": "off",
    // 禁止出现未使用的变量
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
      },
    ],

    // 禁止出现未使用的变量
    "no-unused-vars": [
      "error",
      {
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
      },
    ],
    // ====  跟随geek+前端代码风格指南
    // prettier
    "prettier/prettier": "error",
    // js 要求或禁止文件末尾存在空行
    "eol-last": "error",
    // 禁用行尾空格
    "no-trailing-spaces": "error",
    "comma-style": ["error", "last"],
    "comma-dangle": ["error", "always-multiline"],
    "no-multi-spaces": "error",
    // "no-undef": "error",
    // "no-unused-vars": "error",
    quotes: ["error", "double", { avoidEscape: true, allowTemplateLiterals: true }],

    indent: ["error", 2, { SwitchCase: 1, ignoredNodes: ["ConditionalExpression"] }],

    "object-curly-spacing": ["error", "always"],
    "arrow-parens": ["error", "as-needed"],
    "spaced-comment": ["error", "always"],

    "max-len": ["error", { code: 100, ignoreUrls: true }],

    // vue
    // 在单行元素的内容之前和之后需要换行符
    "vue/singleline-html-element-content-newline": "off",
    // 强制每行的最大属性数
    "vue/max-attributes-per-line": [
      "error",
      {
        singleline: 3,
        multiline: 1,
      },
    ],
    "vue/order-in-components": "off",
    "vue/require-default-prop": "off",
    "vue/html-closing-bracket-spacing": "error",
    "vue/require-prop-types": "off",
    "vue/prop-name-casing": "off",
    "vue/no-template-shadow": "off",
    "vue/no-side-effects-in-computed-properties": "off",
    "vue/no-mutating-props": "off",
    "vue/no-use-v-if-with-v-for": "off",
  },
});
