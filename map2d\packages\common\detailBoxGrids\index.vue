<template>
  <template v-if="data && items?.length">
    <template v-for="(item, index) in items" :key="index">
      <right-grid :label="item.label" :class="{ danger: item?.danger }">
        <slot :name="item.slotName" :item="item">
          {{ itemValue(item) }}
        </slot>
      </right-grid>
    </template>
  </template>
</template>

<script>
import detailMixin from "./rightDetailMixin.js";
import RightGrid from "@map2d/packages/common/right-grid.vue";
export default {
  name: "Map2dRightGrid",
  components: { RightGrid },
  props: {
    data: {
      type: Array,
      isRequired: true,
    },
  },
  mixins: [detailMixin],
  computed: {
    itemValue() {
      return item => {
        const val = item?.isLang ? this.$t(item.value) : item.value;
        const unit = item.unit || "";
        return `${val}${unit}`;
      };
    },

    items() {
      const data = this.data || [];

      let arr = [];
      // 下面调用的方法都在detailMixin中定义
      data.forEach(item => {
        if (!item) return;
        switch (item.validate) {
          case "empty":
            if (this.isNotEmpty(item.value)) arr.push(item);
            break;
          case "position":
            const _p = this.getPosition(item.value);
            if (_p) {
              item.value = _p;
              arr.push(item);
            }
            break;
          case "positionList":
            if (!item.value || !item.value.length) break;
            const _pList = JSON.parse(JSON.stringify(item.value));
            let _pString = "";
            _pList.forEach((v, i) => {
              const _p = this.getPosition(v);
              if (_p) {
                _pString += `${_p}${i < _pList.length - 1 ? "、" : ""}`;
              }
            });
            item.value = _pString;
            arr.push(item);
            break;
          case "CellStatusLang":
            const _cellStatus = this.getCellStatusLang(item.value);
            if (_cellStatus) {
              item.value = _cellStatus;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "CellFlagLang":
            const _cellFlag = this.getCellFlagLang(item.value);
            if (_cellFlag) {
              item.value = _cellFlag;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "ChargeStationStatusLang":
            const _chargerStatus = this.getChargeStationStatusLang(item.value);
            if (_chargerStatus) {
              item.value = _chargerStatus;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "StationTypeLang":
            const _stationType = this.getStationTypeLang(item.value);
            if (_stationType) {
              item.value = _stationType;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "StationPointTypeLang":
            const _stationPointType = this.getStationPointTypeLang(item.value);
            if (_stationPointType) {
              item.value = _stationPointType;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "StationDirectionLang":
            const _stationDirection = this.getStationDirectionLang(item.value);
            if (_stationDirection) {
              item.value = _stationDirection;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "StationBusinessModeLang":
            const _stationBusinessMode = this.getStationBusinessModeLang(item.value);
            if (_stationBusinessMode) {
              item.value = _stationBusinessMode;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "DeviceStatusLang":
            const _deviceStatus = this.getDeviceStatusLang(item.value);
            if (_deviceStatus) {
              item.value = _deviceStatus;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "RobotPathModeLang":
            const _robotPathMode = this.getPathModeLang(item.value);
            if (_robotPathMode) {
              item.value = _robotPathMode;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "TaskTypeLang":
            const _taskType = this.getTaskTypeLang(item.value);
            if (_taskType) {
              item.value = _taskType;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "ShelfStatusLang":
            const _shelfStatus = this.getShelfStatusLang(item.value);
            if (_shelfStatus) {
              item.value = _shelfStatus;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "LatticeStatusLang":
            const _latticeStatus = this.getLatticeStatusLang(item.value);
            if (_latticeStatus) {
              item.value = _latticeStatus;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "LatticeAvailableStatusLang":
            const _latticeAvailableStatus = this.getLatticeAvailableStatusLang(item.value);
            if (_latticeAvailableStatus) {
              item.value = _latticeAvailableStatus;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "PickDirectionLang":
            const _pickDirection = this.getPickDirectionLang(item.value?.[0]);
            if (_pickDirection) {
              item.value = _pickDirection;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "DirectionLang":
            const _directionLang = this.getDirectionLang(item.value);
            if (_directionLang) {
              item.value = _directionLang;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "LatticeTypeLang":
            const _latticeType = this.getLatticeTypeLang(item.value);
            if (_latticeType) {
              item.value = _latticeType;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "BoxStatusLang":
            const _boxStatus = this.getBoxStatusLang(item.value);
            if (_boxStatus) {
              item.value = _boxStatus;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "PlcCheck":
            const _plcCheck = this.getPlcCheck(item.value);
            if (_plcCheck) {
              item.value = _plcCheck;
              item.isLang = true;
              arr.push(item);
            }
            break;
          case "ArrayToString":
            const _string = item.value ? item.value.join(",") : "";
            if (_string) {
              item.value = _string;
              arr.push(item);
            }
            break;
          default:
            arr.push(item);
            break;
        }
      });
      return arr;
    },
  },
};
</script>
