<template>
  <el-dialog
    v-model="visible"
    :title="$t('lang.mb.robotOperate.tips')"
    width="36%"
    top="10vh"
    append-to-body
    modal-append-to-body
    :virtualized="false"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="box-wrap">
      <!-- 动态渲染逻辑 -->
      <template v-if="shouldShow('PP_DEFAULT')">
        <box-layout mode="pp" key="pp" />
      </template>
      <template v-if="shouldShow('PP_LITE')">
        <box-layout mode="pplite" key="pplite" />
      </template>

      <div class="position-hint">
        {{ $t("lang.rms.fed.ppinspect.confirmBoxPosition") }}
      </div>
    </div>

    <template #footer>
      <span>
        <el-button @click="handleClose">{{ $t("lang.common.cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ $t("lang.rms.fed.save") }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import BoxLayout from "./box-layout.vue";

export default {
  name: "CheckTotePosition",
  components: { BoxLayout },
  props: {
    modelCategory: {
      type: String,
      default: "PP_DEFAULT",
      validator: value => ["PP_DEFAULT", "PP_LITE", "BOTH"].includes(value),
    },
  },
  data() {
    return {
      visible: false,
    };
  },
  methods: {
    open() {
      this.visible = true;
    },
    handleSubmit() {
      this.$emit("confirm");
      this.visible = false;
    },
    handleClose() {
      this.visible = false;
    },
    // 条件判断方法
    shouldShow(modeType) {
      if (this.modelCategory === "BOTH") return true;
      return this.modelCategory?.toLowerCase() === `${modeType}`.toLowerCase();
    },
  },
};
</script>

<style lang="less" scoped>
.box-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  .position-hint {
    margin-top: 10px;
    font-size: 15px;
    color: #333;
  }
}
</style>
