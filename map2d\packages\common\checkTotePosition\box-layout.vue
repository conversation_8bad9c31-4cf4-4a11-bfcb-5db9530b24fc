<template>
  <div class="container">
    <div class="box" v-for="(box, index) in computedBoxes" :key="index">
      {{ $t(box) }}
    </div>
  </div>
</template>

<script>
export default {
  name: "BoxLayout",
  props: {
    // 新增的prop定义（支持类型验证）
    mode: {
      type: String,
      default: "pp", // 默认模式
      validator: function (value) {
        return ["pp", "pplite"].includes(value);
      },
    },
  },
  computed: {
    // 使用计算属性动态生成boxes数组
    computedBoxes() {
      return this.mode === "pplite"
        ? ["", "lang.rms.fed.ppinspect.inspectBoxPosition", ""]
        : [
            "",
            "lang.rms.fed.ppinspect.inspectBoxPosition",
            "lang.rms.fed.ppinspect.inspectBoxPosition",
            "",
          ];
    },
  },
};
</script>

<style lang="less" scoped>
@box-padding: 10px;
@box-margin: 5px;
@box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
@box-border-radius: 5px;
@box-font-size: 15px;
@box-font-weight: bold;
@box-color: white;
@box-background-color: #4d87fc;
@box-background-color-light: #f0f0f0;

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid #ccc;
  margin-bottom: 10px;
}

.box {
  background-color: @box-background-color;
  color: @box-color;
  text-align: center;
  padding: @box-padding;
  box-shadow: @box-shadow;
  font-size: @box-font-size;
  font-weight: @box-font-weight;
  border-radius: @box-border-radius;
  margin: 0 @box-margin;
  width: 100px;
  height: 190px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden; /* 如果内容溢出则隐藏 */
  overflow-wrap: break-word; /* 自动换行 */
  word-break: break-word; /* 强制换行 */
  white-space: normal; /* 换行 */
}

/* 第一个和最后一个盒子的样式 */
.box:first-child,
.box:last-child {
  background-color: @box-background-color-light;
}
</style>
