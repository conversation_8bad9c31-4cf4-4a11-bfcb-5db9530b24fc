<template>
  <el-dialog
    v-model="visible"
    width="50%"
    :footer="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    destroy-on-close
    append-to-body
    class="no-header"
  >
    <el-collapse v-model="activeNames" accordion>
      <el-collapse-item
        v-for="(item, index) in list"
        :key="index"
        :title="$t(item.category)"
        :name="index"
      >
        <div v-for="(error, errIndex) in item.errorList" :key="errIndex">
          <p>
            <strong style="font-size: 13px; font-weight: 600">{{ $t(error.object) }}: </strong>
            <label style="font-size: 13px; font-weight: 600">{{ $t(error.exception) }}, </label>
            <span>{{ $t(error.solution) }}</span>
          </p>
          <p v-if="error.detail">
            <label style="font-size: 13px; font-weight: 600">
              {{ $t("lang.rms.diagnosis.web.checkItem") }}:
            </label>
            <span>{{ error.detail }}</span>
          </p>
          <p v-if="error.detailData">
            <label style="font-size: 13px; font-weight: 600">
              {{ $t("lang.rms.config.guide.step.name.config.check") }}:
            </label>
            <span>{{ $t(error.detailData) }}</span>
          </p>
        </div>
      </el-collapse-item>
    </el-collapse>

    <div class="warn-dialog-floor">
      <el-button class="warn-btn" type="primary" @click="handleOk">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from "pinia";
import { useLangStore } from "@stores/langStore";

export default {
  name: "Map2dCheckWarning",
  data() {
    return {
      activeNames: [0],
      list: [],
      visible: false,
    };
  },
  computed: {
    ...mapState(useLangStore, ["isLangDataInit"]),
  },
  watch: {
    isLangDataInit: {
      handler(val) {
        if (val) {
          $req.get("/athena/warehouse/monitor/getSystemStartupAbnormal").then(res => {
            if (!res?.data?.length) return;
            this.list = res.data;
            this.visible = true;
          });
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleOk() {
      $req.post("/athena/warehouse/monitor/confirmSystemStartupAbnormal").then(res => {
        this.visible = false;
      });
    },
  },
};
</script>

<style lang="scss">
.warn-dialog-floor {
  margin-top: 8px;
  text-align: right;

  .warn-btn {
    width: 100px;
  }
}
</style>
