import Cell from "../layer/Cell";
import Charge from "../layer/Charge";
import Station from "../layer/Station";
import Elevator from "../layer/Elevator";
import Marker from "../layer/Marker";
import Safe from "../layer/Safe";
import Line from "../layer/Line";
import Area from "../layer/Area";
import Device from "../layer/Device";
import Conveyor from "../layer/Conveyor";
import Lift from "../layer/Lift";
import Fork from "../layer/Fork";
import MoveFork from "../layer/MoveFork";
import Stacker from "../layer/Stacker";
//用于操作的图层
import Operate from "../layer/Operate";
//背景图
import BackGround from "../layer/BackGround";
const layerConfig = [
  {
    id: "OPERATE",
    layerClass: Operate,
    zIndex: 1000,
  },
  {
    id: "BACKGROUND",
    layerClass: BackGround,
    zIndex: 1,
  },
  {
    id: "CELL",
    layerClass: Cell,
    zIndex: 2,
  },
  {
    id: "STATION",
    layerClass: Station,
    zIndex: 3,
  },
  {
    id: "CHARGER",
    layerClass: Charge,
    zIndex: 4,
  },
  {
    id: "MARKER",
    layerClass: Marker,
    zIndex: 5,
  },
  {
    id: "SAFE",
    layerClass: Safe,
    zIndex: 6,
  },
  {
    id: "ELEVATOR",
    layerClass: Elevator,
    zIndex: 7,
  },
  {
    id: "LINE",
    layerClass: Line,
    zIndex: 8,
  },
  {
    id: "AREA",
    layerClass: Area,
    zIndex: 9,
  },
  {
    id: "DEVICE",
    layerClass: Device,
    zIndex: 3,
  },
  {
    id: "CONVEYOR",
    layerClass: Conveyor,
    zIndex: 4,
  },
  {
    id: "LIFT",
    layerClass: Lift,
    zIndex: 4,
  },
  {
    id: "FORK",
    layerClass: Fork,
    zIndex: 4,
  },
  {
    id: "STACKER",
    layerClass: Stacker,
    zIndex: 4,
  },
  {
    id: "MOVE_FORK",
    layerClass: MoveFork,
    zIndex: 4,
  },
];
export default layerConfig;
