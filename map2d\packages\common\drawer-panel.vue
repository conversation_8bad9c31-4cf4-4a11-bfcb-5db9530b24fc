<template>
  <div ref="drawerPanelRef" v-show="isVisible" class="map2d-right-target-panel">
    <h5 class="header" :class="{ 'is-shrink': shrink }">
      <span class="title" @click="showMore">
        <em>
          {{ $t(title) }}
          <el-icon v-if="shrink" v-show="!isDetailShow"><gp-icon-caret-bottom /></el-icon>
          <el-icon v-if="shrink" v-show="isDetailShow"><gp-icon-caret-top /></el-icon>
        </em>
        <strong v-show="code">{{ code }}</strong>
      </span>
      <el-icon class="close-icon" @click="close"><gp-icon-close /></el-icon>
    </h5>

    <div v-show="isDetailShow" class="content">
      <slot></slot>
    </div>
  </div>
</template>
<script>
import { mapState, mapActions } from "pinia";
import { useMap2dStore } from "@map2d/dataStore/index";
import { useMap2dAbnormalStore } from "@map2d/dataStore/top-panel/panel-abnormal";

export default {
  name: "Map2dRightDrawerPanel",
  props: {
    title: {
      type: String,
      default: "",
    },
    code: {
      type: [String, Number],
      default: "",
    },
    shrink: {
      type: Boolean,
      default: false,
    },
    appendParentId: {
      type: String,
      default: ".single-map2d",
    },
  },
  data() {
    return {
      isVisible: false,

      isDetailShow: false,
    };
  },
  computed: {
    ...mapState(useMap2dStore, ["isRightDrawerVisible"]),
  },
  watch: {
    shrink: {
      handler(flag) {
        this.isDetailShow = !flag;
      },
      immediate: true,
      once: true,
    },
    isRightDrawerVisible(flag) {
      if (!flag) this.isVisible = false;
    },
    code(val) {
      if (!val) this.close();
    },
  },
  mounted() {
    const $drawerPanel = document.querySelector(".map2d-right-target-panel");
    const $map2d = document.querySelector(this.appendParentId);
    if ($drawerPanel && $map2d) {
      $map2d.appendChild($drawerPanel);
    }
  },
  beforeUnmount() {
    const $drawerPanel = document.querySelector(".map2d-right-target-panel");
    const $map2d = document.querySelector(this.appendParentId);
    if ($drawerPanel && $map2d) {
      $map2d.removeChild($drawerPanel);
    }
    this.setRightDrawerVisible(false);
    this.setMapAbnormalPanelVisible(true, "rightDrawerAction");
  },
  methods: {
    ...mapActions(useMap2dStore, ["setRightDrawerVisible"]),
    ...mapActions(useMap2dAbnormalStore, ["setMapAbnormalPanelVisible"]),
    open() {
      if (!this.isVisible && this.shrink && this.code) {
        this.isDetailShow = false;
      }
      this.isVisible = true;
      this.setRightDrawerVisible(true);
      this.setMapAbnormalPanelVisible(false, "rightDrawerAction");
    },
    close() {
      this.isVisible = false;
      this.setRightDrawerVisible(false);
      this.setMapAbnormalPanelVisible(true, "rightDrawerAction");
    },

    showMore() {
      if (!this.shrink) return;
      this.isDetailShow = !this.isDetailShow;
    },
  },
};
</script>

<style lang="less" scoped>
.map2d-right-target-panel {
  position: absolute;
  width: 260px;
  top: @map2d-top-toolbar-height;
  max-height: calc(100% - @map2d-top-toolbar-height - @map2d-bottom-bar-height);
  left: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  background: rgba(206, 218, 236, 0.9);
  box-shadow:
    0 4px 8px 0 rgba(0, 0, 0, 0.2),
    0 6px 20px 0 rgba(0, 0, 0, 0.19);
  z-index: 9;
  display: flex;
  flex-direction: column;

  .header {
    .g-flex();
    padding: 3px 8px;
    .title {
      > em {
        font-size: 13px;
        border-radius: 3px;
        user-select: none;
      }
      > strong {
        margin-left: 6px;
        padding: 2px 6px;
        font-size: 12px;
        font-weight: 600;
        color: #4b5668;
        background: #fff;
        border-radius: 3px;
      }
    }
    .close-icon {
      cursor: pointer;
    }

    &.is-shrink {
      .title {
        > em {
          padding: 2px 6px;
          background: #4a678c;
          color: #fff;
          cursor: pointer;
        }
      }
    }
  }

  .content {
    width: 100%;
    flex: 1;
    padding: 0 4px 8px;
    overflow: auto;
  }
}
</style>
