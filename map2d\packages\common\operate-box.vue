<template>
  <div class="map-right-operate-box">
    <h6>
      <span class="title">{{ $t(title) }}</span>
      <div v-if="tips" class="tips">
        <el-icon class="tips-icon"><gp-icon-info /></el-icon>
        {{ $t(tips) }}
      </div>
    </h6>

    <div class="content">
      <template v-if="operateLayers.length">
        <el-radio-group
          v-model="layerValue"
          size="small"
          fill="#269bff"
          text-color="#fff"
          class="layer-type"
          @layerChange="$emit('layerChange')"
        >
          <el-radio-button v-for="item in operateLayers" :value="item.value" :key="item.value">
            {{ $t(item.name) }}
          </el-radio-button>
        </el-radio-group>
      </template>
      <slot />
    </div>

    <div class="btn-group">
      <el-button size="small" plain @click="$emit('cancel')">
        {{ $t("lang.rms.fed.cancel") }}
      </el-button>
      <el-button size="small" type="primary" @click="$emit('submit')">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "Map2dRightOperateBox",
  props: {
    title: {
      type: String,
      default: "--",
    },
    tips: {
      type: String,
      default: "",
    },
    operateLayers: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      layerValue: "",
    };
  },
  watch: {
    operateLayers: {
      handler(layers) {
        if (layers.length <= 0) return;
        this.layerValue = layers[0].value;
      },
      immediate: true,
    },
    layerValue: {
      handler(val) {
        if (!val) return;
        this.$emit("layerChange", val);
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.map-right-operate-box {
  width: 100%;
  padding-bottom: 8px;
  > h6 {
    position: relative;
    .g-flex();
    justify-content: flex-start;
    padding-left: 3px;
    padding-bottom: 4px;

    &::before {
      content: "";
      display: inline-block;
      height: 13px;
      left: 0;
      position: absolute;
      top: 50%;
      transform: translateY(-60%);
      width: 4px;
      background: #269bff;
    }
    .title {
      font-size: 12px;
      font-style: normal;
      font-weight: 900;
      padding-left: 4px;
      padding-right: 6px;
      line-height: 20px;
      color: #1d222c;
    }

    .tips {
      .g-flex();
      justify-content: flex-start;
      flex: 1;
      font-size: 12px;
      line-height: 1;
      background: #effaff;
      color: #1d222c;

      .tips-icon {
        margin-right: 3px;
        color: #269bff;
      }
    }
  }

  > .content {
    border: 1px #c8cdd3 solid;
    border-bottom: 0;
    overflow: auto;

    :deep(.map-right-grid) {
      &:nth-child(2n) {
        background-color: #f2f3f5;
      }
      .value {
        color: #4b5668;
      }
      .rms-input__inner {
        height: 24px;
        line-height: 24px;
        font-size: 12px;
      }
    }
  }

  :deep(.layer-type) {
    display: block;
    padding: 4px;
    border-bottom: 1px #c8cdd3 solid;
  }

  .btn-group {
    .g-flex();
    justify-content: flex-end;
    padding: 4px;
    border: 1px #c8cdd3 solid;
    border-top: 0;
    background: #fff;
    > .g-button {
      margin-left: 4px;
    }
  }
}
</style>
