/**
 * PPP工作站的 deviceModel
 */
interface PPPDeviceModel {
  extendJson: any;
  id?: number;
  modelName?: string;
  modelCode?: string;
  modelType?: string;
  protocolName?: string;
  specification?: string;
}
/**
 * park
 */
interface ParkInter {
  plcCode: string;
  latticeInfos?: LatticeInter[];
  parkId?: string;
}

/**
 * lattice
 */
interface LatticeInter {
  id?: number;
  latticeCode?: string;
  line: string;
  layerColumnNum: number;
  locationX: number;
  locationY: number;
}
/**
 * PPP 工作站
 */
export interface MapPPPStationDto {
  id?: number;
  mapId: number;
  deviceCode: number | string;
  xlocationOffset: number;
  ylocationOffset: number;
  deviceModel?: PPPDeviceModel;
  extendParkIds?: any[];
  left?: ParkInter;
  right?: ParkInter;
}
