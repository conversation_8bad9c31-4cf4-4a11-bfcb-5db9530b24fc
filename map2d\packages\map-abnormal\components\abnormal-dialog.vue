<template>
  <el-dialog v-model="visible" width="520" append-to-body destroy-on-close>
    <template #header>
      <span class="info-header">
        <el-icon color="#FF7F08" size="18"><ElIconInfoFilled /></el-icon>
        {{ $t("lang.rms.fed.abnormalDetails") }}
      </span>
    </template>
    <div class="confirm-content">
      <p>
        <label>{{ $t("lang.rms.fed.number") }} :</label>
        <span>{{ item.eventObj }}</span>
      </p>
      <p>
        <label>{{ $t("lang.rms.web.monitor.exception.info") }} :</label>
        <span>{{ item._langText }} {{ item.subType ? `(${item.subType})` : "" }}</span>
      </p>
      <p>
        <label>{{ $t("lang.rms.fed.reportTime") }} :</label>
        <span>{{ timeL }}</span>
      </p>
      <p>
        <label>{{ $t("lang.rms.web.monitor.exception.method") }} :</label>
        <span>{{ $t(item.eventSolution || "") }}</span>
      </p>
    </div>

    <template #footer>
      <el-button @click="visible = false">{{ $t("lang.rms.fed.cancel") }}</el-button>
      <el-button v-if="item?.taskId" type="primary" @click="instruction">
        {{ $t("lang.rms.fed.viewTask") }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: "Map2dAbnormalDialog",
  data() {
    return {
      visible: false,
      item: {},
    };
  },
  computed: {
    timeL() {
      return this.item.timeL ? $utils.Tools.formatTimezone(this.item.timeL) : this.item.time;
    },
  },
  methods: {
    open(item) {
      this.item = item;
      this.visible = true;
    },
    instruction() {
      const item = this.item;
      if (item.taskId) {
        // 进入任务
        const router = this.$router.resolve({
          path: "/warehouseManage/taskManage",
          query: {
            taskId: item.taskId,
            taskType: item.taskType,
          },
        });

        window.open(router.href, "_blank");
      }
    },
  },
};
</script>
<style lang="less" scoped>
.info-header {
  display: flex;
  align-items: center;
  padding-bottom: 5px;
  gap: 5px;
  font-size: 16px;
}

.confirm-content {
  padding: 12px;
  display: grid;
  grid-template-columns: auto 1fr; /* 每列占据相等的可用空间 */
  gap: 5px;
  > p {
    display: contents; /* 使得子元素直接成为网格项 */
    font-size: 14px;
    > label {
      text-align: right;
      padding-right: 2px;
      color: #8d97a5;
    }
  }
}
</style>
