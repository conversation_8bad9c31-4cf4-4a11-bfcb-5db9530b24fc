import { defineStore } from "pinia";

export const useMap2dStationStore = defineStore("map2dStationStore", {
  state: () => ({
    stationId: "", // 工作站编号
    /**
     * 当前 工作站 选中的货箱/货位 信息 lattice
     *  { type: "lattice", lattice: data, ___boxLock:true, ___latticeLock:true}
     *  { type: "box", lattice:data, ___boxLock:true, ___latticeLock:true}
     */
    currentSelected: null,
    /**
     * 目标 工作站 选中的格子信息
     * {type: "station", latticeCode} | {type: "poppick", latticeCode}
     */
    targetSelected: null,
  }),
  actions: {
    setStationId(code) {
      if (code == this.stationId) return;
      this.stationId = code;
    },
    setCurrentSelected(data) {
      this.currentSelected = data;
    },
    setTargetSelected(data) {
      this.targetSelected = data;
    },
  },
});
