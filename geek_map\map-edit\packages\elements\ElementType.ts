import { FormItemExPropsType } from "@packages/components/map-form/type";
interface ElementOptionType {
  name?: string;
  nodeType: string;
  nodeIdKey: string;
  nodeTypeKey: string;
}

interface ElementEventType {
  [eventName: string]: Function;
}

export default class ElementType {
  name?: string;
  eventName?: string;
  nodeType: string = "";
  nodeIdKey: string = "";
  nodeTypeKey: string = "";
  eventListener: ElementEventType = {};

  constructor(option: ElementOptionType) {
    const { nodeType, nodeIdKey, nodeTypeKey, name } = option;
    this.name = name;
    this.nodeType = nodeType;
    this.nodeIdKey = nodeIdKey;
    this.nodeTypeKey = nodeTypeKey;
  }

  getFromItem(): FormItemExPropsType[] {
    return [];
  }

  addEventListener(eventName: string, callBack: Function) {
    this.eventListener[eventName] = callBack;
  }
}
