<template>
  <div class="park-item-header">
    <span class="title-label">
      {{ $t("lang.rms.fed.dockPointEncod") }}
      <strong>{{ parkId }}</strong>
    </span>

    <div>
      <span class="hint-icon">
        <el-tooltip
          v-if="inspectExcI18nCode"
          :value="true"
          popper-class="pp-inspec-model-tooltip"
          effect="dark"
          placement="top-end"
          :content="$t(inspectExcI18nCode)"
        >
          <el-icon :size="20"><gp-icon-warning-outline /></el-icon>
        </el-tooltip>
      </span>

      <span class="station-type" :title="busModelText">
        {{ busModelText }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: "map-right-park-item-header",
  props: ["parkId", "busModel", "inspectExcI18nCode"],

  data() {
    return {
      stationTypeDict: {
        TRANSFER: "lang.rms.fed.containerAdjustment",
        MULTIPLE: "lang.rms.fed.integratedMode",
        ENTER: "lang.rms.fed.stationPoint.busModel.enter",
        INSPECT: "lang.rms.fed.stationPoint.busModel.inspection",
        MEANLESS: "MEANLESS",
      },
    };
  },
  computed: {
    busModelText() {
      const dict = this.stationTypeDict[this.busModel];
      if (!dict) {
        return this.busModel;
      }

      return this.$t(dict);
    },
  },
};
</script>

<style lang="less" scoped>
.park-item-header {
  position: relative;
  .g-flex();
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
  color: #269bff;
  background: #effaff;

  .title-label {
    font-size: 12px;
    strong {
      margin-left: 6px;
      font-weight: 900;
    }
  }

  .hint-icon {
    color: red;
  }

  .station-type {
    font-size: 12px;
    padding: 0 4px;
    border-radius: 2px;
    border: 1px solid #269bff;
    height: 20px;
    line-height: 20px;
    max-width: 130px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
