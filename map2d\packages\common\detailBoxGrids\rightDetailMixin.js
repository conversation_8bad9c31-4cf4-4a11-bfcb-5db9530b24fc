export default {
  methods: {
    // 过滤空值函数
    isNotEmpty(data) {
      if (!data && data != false) return false;
      if (data === "-1") return false;

      switch (typeof data) {
        case "string":
          return data.trim().length > 0; // 处理只包含空白字符的字符串
        case "object":
          if (data === null) return false; // 处理 null
          if (Array.isArray(data)) return data.length > 0; // 处理数组
          return Object.keys(data).length > 0; // 处理对象
        case "number":
          if (data === -1) return false;
          return !isNaN(data);
        case "undefined":
          return false;
        default:
          return true;
      }
    },

    getPosition(location) {
      // 检查输入是否为对象且不为null
      if (!location || typeof location !== "object") {
        return undefined;
      }

      if (Array.isArray(location)) {
        if (location.length === 0) return undefined;
        else return location;
      }

      const values = Object.values(location);
      if (values.length === 0) {
        return undefined;
      } else {
        return values;
      }
    },

    // 方向转化为human readable
    getDirectionLang(direction) {
      switch (direction) {
        case "NORTH":
          return "lang.rms.fed.north";
        case "EAST":
          return "lang.rms.fed.east";
        case "SOUTH":
          return "lang.rms.fed.south";
        case "WEST":
          return "lang.rms.fed.west";
        default:
          return "";
      }
    },

    // 取货方向转化为human readable
    getPickDirectionLang(pickDirection) {
      // [0] 东
      // [1] 南
      // [2] 西
      // [3] 北
      switch (pickDirection) {
        case 0:
          return "lang.rms.fed.east";
        case 1:
          return "lang.rms.fed.south";
        case 2:
          return "lang.rms.fed.west";
        case 3:
          return "lang.rms.fed.north";
        default:
          return "";
      }
    },

    // 货位状态转化为human readable
    getLatticeStatusLang(latticeStatus) {
      if (!latticeStatus) return "";
      const newLatticeStatus = this._transformString(latticeStatus);
      return `lang.rms.lattice.latticeState.${newLatticeStatus}`;
    },
    // 货位可用状态转化为human readable
    getLatticeAvailableStatusLang(latticeAvailableStatus) {
      if (!latticeAvailableStatus) return "";
      const newLatticeAvailableStatus = this._transformString(latticeAvailableStatus);
      return `lang.rms.lattice.latticeFlag.${newLatticeAvailableStatus}`;
    },

    // 货位类型转化为human readable
    getLatticeTypeLang(latticeType) {
      if (!latticeType) return "";
      return `lang.rms.fed.latticeType.${latticeType}`;
    },

    getBoxStatusLang(boxStatus) {
      if (!boxStatus) return "";
      const newBoxStatus = this._transformString(boxStatus);
      return `lang.rms.box.boxstate.${newBoxStatus}`;
    },

    // 节点状态转化为国际化
    getCellStatusLang(nodeStatus) {
      if (!nodeStatus) return "";
      const newNodeStatus = this._transformString(nodeStatus);
      return `lang.rms.cell.cellState.${newNodeStatus}`;
    },
    // 单元格 锁定状态 转化为human readable
    getCellFlagLang(cellFlag) {
      if (!cellFlag) return "";
      const newCellFlag = this._transformString(cellFlag);
      return `lang.rms.node.nodeState.${newCellFlag}`;
    },

    // 工作站类型转化为human readable
    getStationTypeLang(stationType) {
      if (!stationType) return "";
      const newStationType = this._transformString(stationType);
      return `lang.rms.common.dict.stationType.${newStationType}`;
    },

    // 停靠点类型转化为human readable
    getStationPointTypeLang(deviceType) {
      if (!deviceType) return "";
      return `lang.rms.fed.stationPoint.deviceType.${deviceType}`;
    },
    // 停靠点工作站方向转化为human readable
    getStationDirectionLang(stationDirection) {
      if (!stationDirection) return "";
      return `lang.rms.fed.stationPlaceDir.${stationDirection}`;
    },
    // 停靠点业务模式转化为human readable
    getStationBusinessModeLang(stationBusinessMode) {
      if (!stationBusinessMode) return "";
      return `lang.rms.fed.stationPoint.busModel.${stationBusinessMode}`;
    },
    // 设备状态转化为human readable
    getDeviceStatusLang(deviceStatus) {
      if (!deviceStatus) return "";
      return `lang.rms.fed.deviceStatus.${deviceStatus}`;
    },

    // 充电站工作状态转化为human readable
    getChargeStationStatusLang(chargeStationStatus) {
      if (!chargeStationStatus) return "";
      const newChargeStationStatus = this._transformString(chargeStationStatus);
      return `lang.rms.charger.status.${newChargeStationStatus}`;
    },

    // 货架状态转化为human readable
    getShelfStatusLang(shelfStatus) {
      if (!shelfStatus) return "";
      const newShelfStatus = this._transformString(shelfStatus);
      return `lang.rms.shelf.state.${newShelfStatus}`;
    },

    // 路径模式转化为human readable
    getPathModeLang(pathMode) {
      if (!pathMode) return "";
      const newPathMode = this._transformString(pathMode);
      return `lang.rms.pathMode.${newPathMode}`;
    },

    // 处理PLC类型 转化为human readable 先判断类型
    getPlcCheck(data) {
      if (typeof data === "boolean") {
        return data ? "lang.rms.fed.yes" : "lang.rms.fed.no";
      } else {
        return data;
      }
    },

    getTaskTypeLang(taskType) {
      if (!taskType) return "";
      return `lang.rms.fed.taskType.${taskType}`;
    },

    _transformString(input) {
      return input
        .split("_")
        .map((word, index) => {
          if (index === 0) {
            return word.toLowerCase(); // 第一个单词全部小写
          } else {
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(); // 其余单词首字母大写，其余小写
          }
        })
        .join("");
    },
  },
};
