/**
 * 这里集中处理保存/初始化数据
 * ⭐⭐⭐⭐⭐⭐⭐⭐⭐⭐
 */
import { MapNodeDto, MapSegmentDto, MapAreaDto } from "@packages/type/editNode";
import { useAttrStore } from "@packages/store/attr";
import { useAppStore } from "@packages/store/app";
import { useEditMap, reloadMapData } from "./useEdit";
import mapVNode from "@packages/hook/useMapVNode";
import { validateMap, saveMap } from "@packages/api/map";
import {
  ValidateMapParams,
  MapFloorDto,
  MoveOperationDtos,
} from "@packages/api/map/type/validateMap";
import { ElMessage } from "element-plus";
import { useI18n } from "@packages/hook/useI18n";

function getSaveParams() {
  const editMap = useEditMap();
  const appStore = useAppStore();
  const mapId: string | number = appStore.mapId || "";
  const floorId: string | number = appStore.floorId || "";
  const mapData = editMap.value?.getAllData();
  const deleteData = editMap.value?.getDeleteData();
  const mapAreaList: any[] = mapData.AREA || [];
  const mapChargerDtoList: any[] = mapData.CHARGER || [];
  const mapDeviceItemDtoList: any[] = [];
  const dmsDeviceDtoList: any[] = [];
  const mapFloorMaskItemDtoList: any[] = [];
  const mapMarkerList: any[] = mapData.MARKER;
  const mapSegmentDtoList: any[] = mapData.LINE || [];
  const mapStationDtoList: any[] = mapData.STATION || [];
  //安全设备
  const dmpDeviceDtoList: any[] = mapData.SAFE || [];
  /**
   * ⭐ 电梯数据在提交的时候会将其中的 queueCellsCodeIdMap 字段移除,
   * 提交此字段后端会报错, 且后端会自动生成该字段
   */
  const mapElevatorDtoList: any[] = (mapData.ELEVATOR || []).map(
    ({ queueCellsCodeIdMap, ...item }: any) => ({
      ...item,
    }),
  );

  /**
   * ⭐ node数据必须带 renderID 而且不能重复...
   */
  const mapNodeDtoList: any[] =
    mapData.CELL.map((item: any, index: number) => {
      return { ...item, renderID: index };
    }) || [];

  const mapFloorDto: MapFloorDto = {
    mapId: Number(mapId),
    floorId: Number(floorId),
    mapAreaList,
    mapChargerDtoList,
    mapDeviceItemDtoList,
    mapFloorMaskItemDtoList,
    mapMarkerList,
    mapNodeDtoList,
    mapSegmentDtoList,
    mapStationDtoList,
    mapElevatorDtoList,
    dmpDeviceDtoList,
    dmsDeviceDtoList,
    deleteData,
  };
  /**
   * moveOperationDtos 全局点位编辑数据
   */
  const moveOperationDtos: MoveOperationDtos[] = useAttrStore().moveOperationDtos || [];

  const validateMapParams: ValidateMapParams = {
    mapId,
    mapFloorDto,
    moveOperationDtos,
  };

  return validateMapParams;
}

/**
 * 保存数据
 * @returns
 */
export const saveEditMap = async () => {
  const { t } = useI18n();
  const validateMapParams = getSaveParams();
  const attrStore = useAttrStore();
  attrStore.setGlobalLoading(true);
  try {
    const { code, data } = await validateMap(<ValidateMapParams>validateMapParams);
    if (code === 0 && Object.keys(data).length === 0) {
      attrStore.setGlobalLoading(false);
      const saveData = await saveMap(validateMapParams);
      if (saveData && saveData.code === 0) {
        attrStore.setStoredCellCodes(validateMapParams.mapFloorDto.mapNodeDtoList);
        ElMessage.success(t("lang.rms.fed.savingSucceeded"));
        const leftPanelRef = mapVNode.useLeftPanels();
        leftPanelRef.setDisabledByName("save", true);
        reloadMapData();
      }
    } else {
      const mapValidateDialog = mapVNode.useMapValidateDialog();
      attrStore.setGlobalLoading(false);
      mapValidateDialog.tootip(data);
    }
  } catch (error) {
    attrStore.setGlobalLoading(false);
  }
};

export async function getMapValidateMap(moveOperationDtos: MoveOperationDtos[]) {
  const validateMapParams = getSaveParams();
  return await validateMap({ ...validateMapParams, moveOperationDtos });
}

export function getMapDataByLocation(option: MapNodeDto) {
  // const { location, startBounds, indexX, indexY, width, length } = option;
  // return {
  //   width,
  //   length,
  //   indexX,
  //   indexY,
  //   locationX: location.x,
  //   locationY: location.y,
  //   startBoundsX: startBounds.x,
  //   startBoundsY: startBounds.y,
  // };
}
